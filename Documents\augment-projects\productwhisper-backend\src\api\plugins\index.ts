import { FastifyInstance } from 'fastify';
import fastifyPlugin from 'fastify-plugin';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import rateLimit from '@fastify/rate-limit';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import websocket from '@fastify/websocket';
import redis from '@fastify/redis';

import { corsConfig, rateLimitConfig, databaseConfig, serverConfig } from '@/config';
import { redisCache } from '@/infrastructure/cache/redis';

export async function setupPlugins(fastify: FastifyInstance) {
  // CORS Plugin
  await fastify.register(cors, {
    origin: (origin, callback) => {
      // Allow requests with no origin (mobile apps, curl, etc.)
      if (!origin) return callback(null, true);

      // Allow configured origins
      const allowedOrigins = corsConfig.origin.split(',').map(o => o.trim());

      if (allowedOrigins.includes(origin) || allowedOrigins.includes('*')) {
        return callback(null, true);
      }

      // In development, allow localhost with any port
      if (serverConfig.nodeEnv === 'development' && origin.includes('localhost')) {
        return callback(null, true);
      }

      return callback(new Error('Not allowed by CORS'), false);
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-Request-ID',
      'X-API-Key',
    ],
  });

  // Security Plugin
  await fastify.register(helmet, {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    crossOriginEmbedderPolicy: false,
  });

  // Redis Plugin
  await fastify.register(redis, {
    host: new URL(databaseConfig.redisUrl).hostname,
    port: parseInt(new URL(databaseConfig.redisUrl).port) || 6379,
    family: 4,
    keyPrefix: 'productwhisper:',
    enableReadyCheck: true,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
  });

  // Rate Limiting Plugin
  await fastify.register(rateLimit, {
    global: true,
    max: rateLimitConfig.maxRequests,
    timeWindow: rateLimitConfig.windowMs,
    redis: redisCache as any,
    keyGenerator: (request) => {
      // Use IP address for rate limiting
      return request.ip || 'unknown';
    },
    errorResponseBuilder: (request, context) => {
      return {
        success: false,
        error: 'Rate limit exceeded',
        code: 'RATE_LIMIT_EXCEEDED',
        details: {
          limit: context.max,
          remaining: context.ttl,
          resetTime: new Date(Date.now() + context.ttl),
        },
        timestamp: new Date().toISOString(),
      };
    },
    addHeaders: {
      'x-ratelimit-limit': true,
      'x-ratelimit-remaining': true,
      'x-ratelimit-reset': true,
    },
  });

  // WebSocket Plugin for real-time features
  await fastify.register(websocket, {
    options: {
      maxPayload: 1048576, // 1MB
      verifyClient: (info) => {
        // Add WebSocket authentication logic here if needed
        return true;
      },
    },
  });

  // Swagger Documentation Plugin
  if (serverConfig.nodeEnv === 'development') {
    await fastify.register(swagger, {
      swagger: {
        info: {
          title: 'ProductWhisper API',
          description: 'API documentation for ProductWhisper backend',
          version: '1.0.0',
          contact: {
            name: 'ProductWhisper Team',
            email: '<EMAIL>',
          },
        },
        host: `localhost:${serverConfig.port}`,
        schemes: ['http', 'https'],
        consumes: ['application/json'],
        produces: ['application/json'],
        tags: [
          { name: 'Health', description: 'Health check endpoints' },
          { name: 'Products', description: 'Product management endpoints' },
          { name: 'Search', description: 'Product search endpoints' },
          { name: 'Sentiment', description: 'Sentiment analysis endpoints' },
          { name: 'Trends', description: 'Trending products endpoints' },
          { name: 'Reddit', description: 'Reddit integration endpoints' },
          { name: 'Chat', description: 'Chat/FAQ endpoints' },
          { name: 'Comparison', description: 'Product comparison endpoints' },
          { name: 'Analytics', description: 'Analytics endpoints' },
        ],
        definitions: {
          ApiResponse: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              data: { type: 'object' },
              error: { type: 'string' },
              message: { type: 'string' },
              timestamp: { type: 'string', format: 'date-time' },
              requestId: { type: 'string' },
            },
          },
          PaginatedResponse: {
            allOf: [
              { $ref: '#/definitions/ApiResponse' },
              {
                type: 'object',
                properties: {
                  pagination: {
                    type: 'object',
                    properties: {
                      page: { type: 'integer' },
                      limit: { type: 'integer' },
                      total: { type: 'integer' },
                      totalPages: { type: 'integer' },
                      hasNext: { type: 'boolean' },
                      hasPrev: { type: 'boolean' },
                    },
                  },
                },
              },
            ],
          },
          Product: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid' },
              name: { type: 'string' },
              description: { type: 'string' },
              category: { type: 'string' },
              brand: { type: 'string' },
              imageUrls: { type: 'array', items: { type: 'string' } },
              metadata: { type: 'object' },
              createdAt: { type: 'string', format: 'date-time' },
              updatedAt: { type: 'string', format: 'date-time' },
            },
          },
          SentimentScore: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid' },
              productId: { type: 'string', format: 'uuid' },
              source: { type: 'string', enum: ['REDDIT', 'TWITTER', 'AMAZON', 'YOUTUBE', 'TIKTOK', 'INSTAGRAM', 'FACEBOOK', 'MANUAL'] },
              overallScore: { type: 'number', minimum: -1, maximum: 1 },
              positiveScore: { type: 'number', minimum: 0, maximum: 1 },
              negativeScore: { type: 'number', minimum: 0, maximum: 1 },
              neutralScore: { type: 'number', minimum: 0, maximum: 1 },
              confidenceScore: { type: 'number', minimum: 0, maximum: 1 },
              sampleSize: { type: 'integer', minimum: 0 },
              lastUpdated: { type: 'string', format: 'date-time' },
            },
          },
        },
        securityDefinitions: {
          apiKey: {
            type: 'apiKey',
            name: 'X-API-Key',
            in: 'header',
          },
        },
      },
    });

    await fastify.register(swaggerUi, {
      routePrefix: '/docs',
      uiConfig: {
        docExpansion: 'list',
        deepLinking: false,
      },
      staticCSP: true,
      transformStaticCSP: (header) => header,
      transformSpecification: (swaggerObject) => {
        return swaggerObject;
      },
      transformSpecificationClone: true,
    });
  }

  // Custom plugin for request context
  await fastify.register(
    fastifyPlugin(async (fastify) => {
      fastify.decorateRequest('context', null);

      fastify.addHook('onRequest', async (request) => {
        // Add request context data
        (request as any).context = {
          requestId: request.id,
          startTime: Date.now(),
          ip: request.ip,
          userAgent: request.headers['user-agent'],
        };
      });
    })
  );

  // Custom plugin for response helpers
  await fastify.register(
    fastifyPlugin(async (fastify) => {
      fastify.decorateReply('success', function (data: any, message?: string) {
        return this.send({
          success: true,
          data,
          message,
          timestamp: new Date().toISOString(),
          requestId: this.request.id,
        });
      });

      fastify.decorateReply('error', function (error: string, statusCode: number = 500) {
        return this.status(statusCode).send({
          success: false,
          error,
          timestamp: new Date().toISOString(),
          requestId: this.request.id,
        });
      });

      fastify.decorateReply('paginated', function (
        data: any[],
        page: number,
        limit: number,
        total: number,
        message?: string
      ) {
        const totalPages = Math.ceil(total / limit);

        return this.send({
          success: true,
          data,
          message,
          timestamp: new Date().toISOString(),
          requestId: this.request.id,
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
          },
        });
      });
    })
  );

  fastify.log.info('✅ All plugins registered successfully');
}
