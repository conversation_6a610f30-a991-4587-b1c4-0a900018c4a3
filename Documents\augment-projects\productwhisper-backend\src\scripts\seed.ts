import { PrismaClient } from '@prisma/client';
import { PlatformSource } from '@/shared/types';

const prisma = new PrismaClient();

const sampleProducts = [
  {
    name: 'iPhone 15 Pro',
    description: 'The latest iPhone with advanced camera system, A17 Pro chip, and titanium design.',
    category: 'Electronics',
    brand: 'Apple',
    imageUrls: [
      'https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/iphone-15-pro-finish-select-202309-6-1inch-naturaltitanium.jpg'
    ],
    externalIds: {
      amazon: 'B0CHWRXH8B',
      reddit: 'iphone_15_pro'
    },
    metadata: {
      price: 999,
      currency: 'USD',
      rating: 4.5,
      reviewCount: 1250,
      availability: 'in_stock',
      features: ['A17 Pro chip', '48MP camera', 'Titanium design', '5G'],
      specifications: {
        display: '6.1-inch Super Retina XDR',
        storage: '128GB',
        color: 'Natural Titanium'
      },
      tags: ['smartphone', 'premium', 'camera', 'apple']
    }
  },
  {
    name: 'MacBook Air M2',
    description: 'Supercharged by the M2 chip, the redesigned MacBook Air combines incredible performance and up to 18 hours of battery life.',
    category: 'Computers',
    brand: 'Apple',
    imageUrls: [
      'https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/macbook-air-midnight-select-20220606.jpg'
    ],
    externalIds: {
      amazon: 'B0B3C2R8MP',
      reddit: 'macbook_air_m2'
    },
    metadata: {
      price: 1199,
      currency: 'USD',
      rating: 4.7,
      reviewCount: 890,
      availability: 'in_stock',
      features: ['M2 chip', '13.6-inch display', 'All-day battery', 'Silent design'],
      specifications: {
        processor: 'Apple M2',
        memory: '8GB',
        storage: '256GB SSD',
        display: '13.6-inch Liquid Retina'
      },
      tags: ['laptop', 'ultrabook', 'apple', 'productivity']
    }
  },
  {
    name: 'Samsung Galaxy S24 Ultra',
    description: 'The ultimate Android flagship with S Pen, advanced AI features, and professional-grade cameras.',
    category: 'Electronics',
    brand: 'Samsung',
    imageUrls: [
      'https://images.samsung.com/is/image/samsung/p6pim/us/2401/gallery/us-galaxy-s24-ultra-s928-sm-s928uzaaxaa-thumb-539573016.jpg'
    ],
    externalIds: {
      amazon: 'B0CMDWC436',
      reddit: 'galaxy_s24_ultra'
    },
    metadata: {
      price: 1299,
      currency: 'USD',
      rating: 4.4,
      reviewCount: 756,
      availability: 'in_stock',
      features: ['S Pen', '200MP camera', 'AI features', '5000mAh battery'],
      specifications: {
        display: '6.8-inch Dynamic AMOLED 2X',
        storage: '256GB',
        ram: '12GB',
        color: 'Titanium Gray'
      },
      tags: ['smartphone', 'android', 'premium', 's-pen', 'camera']
    }
  },
  {
    name: 'Sony WH-1000XM5',
    description: 'Industry-leading noise canceling headphones with exceptional sound quality and all-day comfort.',
    category: 'Audio',
    brand: 'Sony',
    imageUrls: [
      'https://sony.scene7.com/is/image/sonyglobalsolutions/wh-1000xm5_Primary_image.jpg'
    ],
    externalIds: {
      amazon: 'B09XS7JWHH',
      reddit: 'sony_wh1000xm5'
    },
    metadata: {
      price: 399,
      currency: 'USD',
      rating: 4.6,
      reviewCount: 2100,
      availability: 'in_stock',
      features: ['Noise canceling', '30-hour battery', 'Quick charge', 'Multipoint connection'],
      specifications: {
        type: 'Over-ear',
        wireless: 'Bluetooth 5.2',
        batteryLife: '30 hours',
        weight: '250g'
      },
      tags: ['headphones', 'noise-canceling', 'wireless', 'premium']
    }
  },
  {
    name: 'Nintendo Switch OLED',
    description: 'Enhanced Nintendo Switch with vibrant OLED screen, improved audio, and enhanced kickstand.',
    category: 'Gaming',
    brand: 'Nintendo',
    imageUrls: [
      'https://assets.nintendo.com/image/upload/c_fill,w_1200/q_auto:best/f_auto/dpr_2.0/ncom/software/switch/70010000000964/811316ca6c7a5e05b0b3b8b8b0b3b8b8b0b3b8b8'
    ],
    externalIds: {
      amazon: 'B098RKWHHZ',
      reddit: 'nintendo_switch_oled'
    },
    metadata: {
      price: 349,
      currency: 'USD',
      rating: 4.8,
      reviewCount: 1850,
      availability: 'in_stock',
      features: ['7-inch OLED screen', 'Enhanced audio', 'Wide adjustable stand', '64GB storage'],
      specifications: {
        display: '7-inch OLED',
        storage: '64GB',
        batteryLife: '4.5-9 hours',
        connectivity: 'Wi-Fi, Bluetooth'
      },
      tags: ['gaming', 'console', 'portable', 'nintendo', 'oled']
    }
  }
];

const sampleSentimentScores = [
  {
    source: PlatformSource.REDDIT,
    overallScore: 0.75,
    positiveScore: 0.8,
    negativeScore: 0.1,
    neutralScore: 0.1,
    confidenceScore: 0.85,
    sampleSize: 150
  },
  {
    source: PlatformSource.AMAZON,
    overallScore: 0.65,
    positiveScore: 0.7,
    negativeScore: 0.15,
    neutralScore: 0.15,
    confidenceScore: 0.9,
    sampleSize: 1250
  }
];

const sampleMentions = [
  {
    source: PlatformSource.REDDIT,
    sourceId: 'reddit_post_123',
    content: 'Just got the new iPhone 15 Pro and the camera quality is absolutely amazing! The titanium build feels premium.',
    sentimentScore: 0.8,
    engagementMetrics: {
      upvotes: 45,
      downvotes: 2,
      comments: 12,
      score: 43
    },
    url: 'https://reddit.com/r/iphone/comments/example',
    authorInfo: {
      username: 'tech_enthusiast',
      karma: 15000,
      accountAge: 365
    },
    createdAt: new Date('2024-01-15T10:30:00Z')
  },
  {
    source: PlatformSource.REDDIT,
    sourceId: 'reddit_post_124',
    content: 'The MacBook Air M2 is perfect for students. Great battery life and performance for coding.',
    sentimentScore: 0.7,
    engagementMetrics: {
      upvotes: 32,
      downvotes: 1,
      comments: 8,
      score: 31
    },
    url: 'https://reddit.com/r/macbook/comments/example2',
    authorInfo: {
      username: 'student_dev',
      karma: 5000,
      accountAge: 180
    },
    createdAt: new Date('2024-01-14T15:45:00Z')
  }
];

async function main() {
  console.log('🌱 Starting database seeding...');

  try {
    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await prisma.productMention.deleteMany();
    await prisma.sentimentScore.deleteMany();
    await prisma.product.deleteMany();

    // Create products
    console.log('📦 Creating sample products...');
    const createdProducts = [];
    
    for (const productData of sampleProducts) {
      const product = await prisma.product.create({
        data: productData
      });
      createdProducts.push(product);
      console.log(`✅ Created product: ${product.name}`);
    }

    // Create sentiment scores for products
    console.log('📊 Creating sentiment scores...');
    for (let i = 0; i < createdProducts.length; i++) {
      const product = createdProducts[i];
      
      for (const sentimentData of sampleSentimentScores) {
        await prisma.sentimentScore.create({
          data: {
            ...sentimentData,
            productId: product.id
          }
        });
      }
      console.log(`✅ Created sentiment scores for: ${product.name}`);
    }

    // Create product mentions
    console.log('💬 Creating product mentions...');
    for (let i = 0; i < Math.min(createdProducts.length, sampleMentions.length); i++) {
      const product = createdProducts[i];
      const mentionData = sampleMentions[i];
      
      await prisma.productMention.create({
        data: {
          ...mentionData,
          productId: product.id
        }
      });
      console.log(`✅ Created mention for: ${product.name}`);
    }

    // Create some search analytics
    console.log('🔍 Creating search analytics...');
    const searchQueries = [
      'best smartphone 2024',
      'macbook air review',
      'noise canceling headphones',
      'nintendo switch games',
      'samsung galaxy comparison'
    ];

    for (const query of searchQueries) {
      await prisma.searchAnalytics.create({
        data: {
          query,
          resultsCount: Math.floor(Math.random() * 50) + 10,
          ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0 (compatible; ProductWhisper/1.0)'
        }
      });
    }

    console.log('✅ Search analytics created');

    console.log('\n🎉 Database seeding completed successfully!');
    console.log(`📊 Created ${createdProducts.length} products`);
    console.log(`📈 Created ${createdProducts.length * sampleSentimentScores.length} sentiment scores`);
    console.log(`💬 Created ${Math.min(createdProducts.length, sampleMentions.length)} mentions`);
    console.log(`🔍 Created ${searchQueries.length} search analytics entries`);

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
