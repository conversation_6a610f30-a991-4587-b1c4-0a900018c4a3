import { FastifyInstance } from 'fastify';
import { buildApp } from '../../src/server';

describe('API Integration Tests', () => {
  let app: FastifyInstance;

  beforeAll(async () => {
    app = buildApp();
    await app.ready();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Health Checks', () => {
    test('GET /health should return 200', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/health'
      });

      expect(response.statusCode).toBe(200);
      expect(JSON.parse(response.payload)).toMatchObject({
        success: true,
        data: expect.objectContaining({
          status: 'healthy',
          timestamp: expect.any(String)
        })
      });
    });

    test('GET /ready should return 200', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/ready'
      });

      expect(response.statusCode).toBe(200);
    });
  });

  describe('Product API', () => {
    test('GET /api/v1/products should return products list', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/products'
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.payload);
      expect(body).toMatchObject({
        success: true,
        data: expect.any(Array),
        pagination: expect.objectContaining({
          page: expect.any(Number),
          limit: expect.any(Number),
          total: expect.any(Number)
        })
      });
    });

    test('GET /api/v1/products/categories should return categories', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/products/meta/categories'
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.payload);
      expect(body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          categories: expect.any(Array)
        })
      });
    });
  });

  describe('Search API', () => {
    test('GET /api/v1/search should require query parameter', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/search'
      });

      expect(response.statusCode).toBe(400);
    });

    test('GET /api/v1/search?q=test should return search results', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/search?q=iPhone'
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.payload);
      expect(body).toMatchObject({
        success: true,
        data: expect.any(Array),
        pagination: expect.any(Object)
      });
    });

    test('GET /api/v1/search/suggestions should return suggestions', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/search/suggestions?q=iph'
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.payload);
      expect(body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          suggestions: expect.any(Array),
          query: 'iph'
        })
      });
    });
  });

  describe('Sentiment API', () => {
    test('POST /api/v1/sentiment/analyze should analyze sentiment', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/sentiment/analyze',
        payload: {
          text: 'This product is amazing! I love it.',
          context: 'product_review'
        }
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.payload);
      expect(body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          analysis: expect.objectContaining({
            overallScore: expect.any(Number),
            confidence: expect.any(Number),
            label: expect.stringMatching(/positive|negative|neutral/)
          })
        })
      });
    });

    test('POST /api/v1/sentiment/analyze/batch should handle batch analysis', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/sentiment/analyze/batch',
        payload: {
          texts: [
            'This product is great!',
            'Not impressed with the quality.',
            'It\'s okay, nothing special.'
          ],
          context: 'product_review'
        }
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.payload);
      expect(body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          batchAnalysis: expect.objectContaining({
            results: expect.any(Array),
            totalTexts: 3,
            successCount: expect.any(Number)
          })
        })
      });
    });

    test('GET /api/v1/sentiment/health should return health status', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/sentiment/health'
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.payload);
      expect(body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          isHealthy: expect.any(Boolean),
          service: 'Sentiment Analysis API'
        })
      });
    });
  });

  describe('Reddit API', () => {
    test('GET /api/v1/reddit/search should require productName', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/reddit/search'
      });

      expect(response.statusCode).toBe(400);
    });

    test('GET /api/v1/reddit/subreddits/popular should return popular subreddits', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/reddit/subreddits/popular'
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.payload);
      expect(body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          subreddits: expect.any(Array),
          totalCount: expect.any(Number)
        })
      });
    });

    test('GET /api/v1/reddit/trending should return trending products', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/reddit/trending'
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.payload);
      expect(body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          trendingProducts: expect.any(Array),
          timeframe: expect.any(String)
        })
      });
    });

    test('GET /api/v1/reddit/health should return Reddit API health', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/reddit/health'
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.payload);
      expect(body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          isHealthy: expect.any(Boolean),
          service: 'Reddit API'
        })
      });
    });
  });

  describe('Error Handling', () => {
    test('GET /api/v1/nonexistent should return 404', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/nonexistent'
      });

      expect(response.statusCode).toBe(404);
    });

    test('POST /api/v1/sentiment/analyze with invalid data should return 400', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/sentiment/analyze',
        payload: {
          // Missing required 'text' field
          context: 'product_review'
        }
      });

      expect(response.statusCode).toBe(400);
    });
  });
});
