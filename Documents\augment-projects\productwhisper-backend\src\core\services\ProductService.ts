import { ProductRepository, PrismaProductRepository } from '@/core/repositories/ProductRepository';
import { ProductEntity, ProductWithSentiment, ProductMentionEntity } from '@/core/entities/Product';
import { ProductSearchFilters, ProductSearchSort, PaginationParams, PlatformSource } from '@/shared/types';
import { CacheService } from '@/infrastructure/cache/CacheService';
import { NotFoundError, ValidationError } from '@/shared/errors';
import { CACHE_KEYS, CACHE_TTL } from '@/shared/constants';
import { createCacheKey } from '@/shared/utils';

export class ProductService {
  private productRepository: ProductRepository;
  private cacheService: CacheService;

  constructor() {
    this.productRepository = new PrismaProductRepository();
    this.cacheService = new CacheService();
  }

  async searchProducts(
    query?: string,
    filters: ProductSearchFilters = {},
    sort: ProductSearchSort = { field: 'createdAt', order: 'desc' },
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<{ products: ProductEntity[]; total: number }> {
    // Create cache key based on search parameters
    const cacheKey = createCacheKey(
      CACHE_KEYS.PRODUCTS_SEARCH,
      JSON.stringify({ query, filters, sort, pagination })
    );

    // Try to get from cache first
    const cached = await this.cacheService.get<{ products: ProductEntity[]; total: number }>(cacheKey);
    if (cached) {
      return cached;
    }

    // If query is provided, add it to filters
    if (query) {
      // This would be implemented as a full-text search in the repository
      filters.query = query;
    }

    const result = await this.productRepository.search(filters, sort, pagination);

    // Cache the result
    await this.cacheService.set(cacheKey, result, { ttl: CACHE_TTL.SHORT });

    return result;
  }

  async getProductById(id: string): Promise<ProductEntity | null> {
    // Try cache first
    const cacheKey = createCacheKey(CACHE_KEYS.PRODUCT, id);
    const cached = await this.cacheService.get<ProductEntity>(cacheKey);
    if (cached) {
      return cached;
    }

    const product = await this.productRepository.findById(id);
    
    if (product) {
      // Cache the product
      await this.cacheService.set(cacheKey, product, { ttl: CACHE_TTL.MEDIUM });
    }

    return product;
  }

  async getProductWithSentiment(id: string): Promise<ProductWithSentiment | null> {
    // Try cache first
    const cacheKey = createCacheKey(CACHE_KEYS.PRODUCT, id, 'sentiment');
    const cached = await this.cacheService.get<ProductWithSentiment>(cacheKey);
    if (cached) {
      return cached;
    }

    const product = await this.productRepository.findWithSentiment(id);
    
    if (product) {
      // Cache the product with sentiment
      await this.cacheService.set(cacheKey, product, { ttl: CACHE_TTL.SHORT });
    }

    return product;
  }

  async getProductMentions(
    productId: string,
    filters: { source?: string } = {},
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<{ mentions: ProductMentionEntity[]; total: number }> {
    // Verify product exists
    const product = await this.getProductById(productId);
    if (!product) {
      throw new NotFoundError('Product');
    }

    // Create cache key
    const cacheKey = createCacheKey(
      CACHE_KEYS.PRODUCT,
      productId,
      'mentions',
      JSON.stringify({ filters, pagination })
    );

    // Try cache first
    const cached = await this.cacheService.get<{ mentions: ProductMentionEntity[]; total: number }>(cacheKey);
    if (cached) {
      return cached;
    }

    let result;
    if (filters.source) {
      result = await this.productRepository.getMentionsBySource(productId, filters.source, pagination);
    } else {
      result = await this.productRepository.getMentions(productId, pagination);
    }

    // Cache the result
    await this.cacheService.set(cacheKey, result, { ttl: CACHE_TTL.SHORT });

    return result;
  }

  async createProduct(productData: Omit<ProductEntity, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProductEntity> {
    // Validate product data
    this.validateProductData(productData);

    // Check if product with same name already exists
    const existingProduct = await this.productRepository.findByName(productData.name);
    if (existingProduct) {
      throw new ValidationError('Product with this name already exists');
    }

    const product = await this.productRepository.create(productData);

    // Invalidate related caches
    await this.invalidateProductCaches();

    return product;
  }

  async updateProduct(id: string, updateData: Partial<ProductEntity>): Promise<ProductEntity> {
    // Verify product exists
    const existingProduct = await this.getProductById(id);
    if (!existingProduct) {
      throw new NotFoundError('Product');
    }

    // If name is being updated, check for duplicates
    if (updateData.name && updateData.name !== existingProduct.name) {
      const duplicateProduct = await this.productRepository.findByName(updateData.name);
      if (duplicateProduct && duplicateProduct.id !== id) {
        throw new ValidationError('Product with this name already exists');
      }
    }

    const product = await this.productRepository.update(id, updateData);

    // Invalidate caches
    await this.invalidateProductCache(id);
    await this.invalidateProductCaches();

    return product;
  }

  async deleteProduct(id: string): Promise<boolean> {
    // Verify product exists
    const product = await this.getProductById(id);
    if (!product) {
      return false;
    }

    const deleted = await this.productRepository.delete(id);

    if (deleted) {
      // Invalidate caches
      await this.invalidateProductCache(id);
      await this.invalidateProductCaches();
    }

    return deleted;
  }

  async getCategories(): Promise<{ category: string; count: number }[]> {
    const cacheKey = createCacheKey(CACHE_KEYS.PRODUCT, 'categories');
    
    // Try cache first
    const cached = await this.cacheService.get<{ category: string; count: number }[]>(cacheKey);
    if (cached) {
      return cached;
    }

    const categories = await this.productRepository.countByCategory();

    // Cache the result
    await this.cacheService.set(cacheKey, categories, { ttl: CACHE_TTL.LONG });

    return categories;
  }

  async getBrands(): Promise<{ brand: string; count: number }[]> {
    const cacheKey = createCacheKey(CACHE_KEYS.PRODUCT, 'brands');
    
    // Try cache first
    const cached = await this.cacheService.get<{ brand: string; count: number }[]>(cacheKey);
    if (cached) {
      return cached;
    }

    const brands = await this.productRepository.countByBrand();

    // Cache the result
    await this.cacheService.set(cacheKey, brands, { ttl: CACHE_TTL.LONG });

    return brands;
  }

  async getRecentProducts(limit: number = 10): Promise<ProductEntity[]> {
    const cacheKey = createCacheKey(CACHE_KEYS.PRODUCT, 'recent', limit.toString());
    
    // Try cache first
    const cached = await this.cacheService.get<ProductEntity[]>(cacheKey);
    if (cached) {
      return cached;
    }

    const products = await this.productRepository.findRecentlyAdded(limit);

    // Cache the result
    await this.cacheService.set(cacheKey, products, { ttl: CACHE_TTL.SHORT });

    return products;
  }

  async getPopularProducts(limit: number = 10): Promise<ProductWithSentiment[]> {
    const cacheKey = createCacheKey(CACHE_KEYS.PRODUCT, 'popular', limit.toString());
    
    // Try cache first
    const cached = await this.cacheService.get<ProductWithSentiment[]>(cacheKey);
    if (cached) {
      return cached;
    }

    const products = await this.productRepository.findPopular(limit);

    // Cache the result
    await this.cacheService.set(cacheKey, products, { ttl: CACHE_TTL.SHORT });

    return products;
  }

  async getTrendingProducts(limit: number = 10): Promise<ProductWithSentiment[]> {
    const cacheKey = createCacheKey(CACHE_KEYS.TRENDING, 'products', limit.toString());
    
    // Try cache first
    const cached = await this.cacheService.get<ProductWithSentiment[]>(cacheKey);
    if (cached) {
      return cached;
    }

    const products = await this.productRepository.findTrending(limit);

    // Cache the result
    await this.cacheService.set(cacheKey, products, { ttl: CACHE_TTL.SHORT });

    return products;
  }

  // Helper methods
  private validateProductData(productData: Partial<ProductEntity>): void {
    if (productData.name && productData.name.trim().length === 0) {
      throw new ValidationError('Product name cannot be empty');
    }

    if (productData.name && productData.name.length > 255) {
      throw new ValidationError('Product name cannot exceed 255 characters');
    }

    if (productData.description && productData.description.length > 5000) {
      throw new ValidationError('Product description cannot exceed 5000 characters');
    }

    if (productData.category && productData.category.length > 100) {
      throw new ValidationError('Product category cannot exceed 100 characters');
    }

    if (productData.brand && productData.brand.length > 100) {
      throw new ValidationError('Product brand cannot exceed 100 characters');
    }

    if (productData.imageUrls && productData.imageUrls.length > 10) {
      throw new ValidationError('Product cannot have more than 10 image URLs');
    }
  }

  private async invalidateProductCache(productId: string): Promise<void> {
    const patterns = [
      createCacheKey(CACHE_KEYS.PRODUCT, productId),
      createCacheKey(CACHE_KEYS.PRODUCT, productId, '*'),
    ];

    for (const pattern of patterns) {
      await this.cacheService.deleteByPattern(pattern);
    }
  }

  private async invalidateProductCaches(): Promise<void> {
    const patterns = [
      createCacheKey(CACHE_KEYS.PRODUCTS_SEARCH, '*'),
      createCacheKey(CACHE_KEYS.PRODUCT, 'categories'),
      createCacheKey(CACHE_KEYS.PRODUCT, 'brands'),
      createCacheKey(CACHE_KEYS.PRODUCT, 'recent', '*'),
      createCacheKey(CACHE_KEYS.PRODUCT, 'popular', '*'),
      createCacheKey(CACHE_KEYS.TRENDING, '*'),
    ];

    for (const pattern of patterns) {
      await this.cacheService.deleteByPattern(pattern);
    }
  }
}
