import { HTTP_STATUS } from '@/shared/constants';

export class AppError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly code?: string;
  public readonly details?: any;

  constructor(
    message: string,
    statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR,
    isOperational: boolean = true,
    code?: string,
    details?: any
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;
    this.details = details;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, HTTP_STATUS.BAD_REQUEST, true, 'VALIDATION_ERROR', details);
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, HTTP_STATUS.NOT_FOUND, true, 'NOT_FOUND');
  }
}

export class ConflictError extends AppError {
  constructor(message: string, details?: any) {
    super(message, HTTP_STATUS.CONFLICT, true, 'CONFLICT', details);
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized') {
    super(message, HTTP_STATUS.UNAUTHORIZED, true, 'UNAUTHORIZED');
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden') {
    super(message, HTTP_STATUS.FORBIDDEN, true, 'FORBIDDEN');
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded', retryAfter?: number) {
    super(message, HTTP_STATUS.TOO_MANY_REQUESTS, true, 'RATE_LIMIT_EXCEEDED', {
      retryAfter,
    });
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, message: string, details?: any) {
    super(
      `${service} service error: ${message}`,
      HTTP_STATUS.BAD_GATEWAY,
      true,
      'EXTERNAL_SERVICE_ERROR',
      { service, ...details }
    );
  }
}

export class DatabaseError extends AppError {
  constructor(message: string, details?: any) {
    super(message, HTTP_STATUS.INTERNAL_SERVER_ERROR, true, 'DATABASE_ERROR', details);
  }
}

export class CacheError extends AppError {
  constructor(message: string, details?: any) {
    super(message, HTTP_STATUS.INTERNAL_SERVER_ERROR, true, 'CACHE_ERROR', details);
  }
}

export class RedditApiError extends ExternalServiceError {
  constructor(message: string, details?: any) {
    super('Reddit API', message, details);
  }
}

export class SentimentApiError extends ExternalServiceError {
  constructor(message: string, details?: any) {
    super('Sentiment Analysis API', message, details);
  }
}

// Error handler utility functions
export const isOperationalError = (error: Error): boolean => {
  if (error instanceof AppError) {
    return error.isOperational;
  }
  return false;
};

export const getErrorResponse = (error: AppError | Error) => {
  if (error instanceof AppError) {
    return {
      success: false,
      error: error.message,
      code: error.code,
      details: error.details,
      timestamp: new Date().toISOString(),
    };
  }

  // For non-operational errors, don't expose internal details
  return {
    success: false,
    error: 'Internal server error',
    code: 'INTERNAL_ERROR',
    timestamp: new Date().toISOString(),
  };
};

// Async error wrapper for route handlers
export const asyncHandler = (fn: Function) => {
  return (req: any, reply: any, next?: any) => {
    Promise.resolve(fn(req, reply, next)).catch(next);
  };
};

// Error validation helper
export const createValidationError = (field: string, message: string) => {
  return new ValidationError(`Validation failed for field '${field}': ${message}`, {
    field,
    message,
  });
};

// HTTP status code helpers
export const isClientError = (statusCode: number): boolean => {
  return statusCode >= 400 && statusCode < 500;
};

export const isServerError = (statusCode: number): boolean => {
  return statusCode >= 500 && statusCode < 600;
};

// Error logging helper
export const getErrorLogLevel = (error: AppError | Error): string => {
  if (error instanceof AppError) {
    if (isClientError(error.statusCode)) {
      return 'warn';
    }
    if (isServerError(error.statusCode)) {
      return 'error';
    }
  }
  return 'error';
};
