import { FastifyInstance } from 'fastify';
import { setupServer } from '@/server';
import { ProductController } from '@/api/controllers/ProductController';

describe('ProductController', () => {
  let app: FastifyInstance;
  let productController: ProductController;

  beforeAll(async () => {
    app = await setupServer();
    productController = new ProductController();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /api/v1/products', () => {
    it('should return empty products list initially', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/products',
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data).toEqual([]);
      expect(body.pagination).toBeDefined();
    });

    it('should validate query parameters', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/products?page=0', // Invalid page
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toContain('Validation');
    });
  });

  describe('POST /api/v1/products', () => {
    it('should create a new product', async () => {
      const productData = {
        name: 'Test Product',
        description: 'A test product',
        category: 'Electronics',
        brand: 'TestBrand',
        imageUrls: ['https://example.com/image.jpg'],
        metadata: { price: 99.99 },
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/products',
        payload: productData,
      });

      expect(response.statusCode).toBe(201);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data.name).toBe(productData.name);
      expect(body.data.id).toBeDefined();
    });

    it('should validate required fields', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/products',
        payload: {}, // Missing required name field
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toContain('Validation');
    });
  });

  describe('GET /api/v1/products/:id', () => {
    it('should return 404 for non-existent product', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/products/00000000-0000-0000-0000-000000000000',
      });

      expect(response.statusCode).toBe(404);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.error).toContain('not found');
    });

    it('should validate UUID format', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/products/invalid-uuid',
      });

      expect(response.statusCode).toBe(400);
    });
  });
});
