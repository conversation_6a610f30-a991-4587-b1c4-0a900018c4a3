import { PrismaClient } from '@prisma/client';
import { serverConfig } from '@/config';
import { DatabaseError } from '@/shared/errors';

// Extend PrismaClient with custom methods if needed
class ExtendedPrismaClient extends PrismaClient {
  constructor() {
    super({
      log: serverConfig.nodeEnv === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
      errorFormat: 'pretty',
    });
  }

  async onModuleInit() {
    try {
      await this.$connect();
      console.log('✅ Database connected successfully');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw new DatabaseError('Failed to connect to database');
    }
  }

  async onModuleDestroy() {
    await this.$disconnect();
    console.log('🔌 Database disconnected');
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      await this.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }

  // Transaction wrapper with retry logic
  async withRetry<T>(
    operation: (prisma: ExtendedPrismaClient) => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation(this);
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on certain errors
        if (
          error instanceof Error &&
          (error.message.includes('Unique constraint') ||
           error.message.includes('Foreign key constraint'))
        ) {
          throw error;
        }

        if (attempt === maxRetries) break;
        
        // Exponential backoff
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw new DatabaseError(`Operation failed after ${maxRetries} attempts: ${lastError!.message}`);
  }

  // Soft delete helper
  async softDelete(model: string, id: string) {
    const modelDelegate = (this as any)[model];
    if (!modelDelegate) {
      throw new DatabaseError(`Model ${model} not found`);
    }

    return modelDelegate.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }

  // Bulk operations helper
  async bulkUpsert<T>(
    model: string,
    data: T[],
    uniqueFields: string[]
  ) {
    const modelDelegate = (this as any)[model];
    if (!modelDelegate) {
      throw new DatabaseError(`Model ${model} not found`);
    }

    const operations = data.map((item: any) => {
      const where = uniqueFields.reduce((acc, field) => {
        acc[field] = item[field];
        return acc;
      }, {} as any);

      return modelDelegate.upsert({
        where,
        update: item,
        create: item,
      });
    });

    return this.$transaction(operations);
  }
}

// Create singleton instance
let prisma: ExtendedPrismaClient;

if (serverConfig.nodeEnv === 'production') {
  prisma = new ExtendedPrismaClient();
} else {
  // In development, use a global variable to preserve the instance across hot reloads
  const globalWithPrisma = globalThis as typeof globalThis & {
    prisma: ExtendedPrismaClient;
  };

  if (!globalWithPrisma.prisma) {
    globalWithPrisma.prisma = new ExtendedPrismaClient();
  }

  prisma = globalWithPrisma.prisma;
}

// Initialize connection
prisma.onModuleInit().catch((error) => {
  console.error('Failed to initialize database:', error);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  await prisma.onModuleDestroy();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.onModuleDestroy();
  process.exit(0);
});

export { prisma };
export default prisma;
