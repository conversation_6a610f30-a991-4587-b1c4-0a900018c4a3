// API Constants
export const API_ROUTES = {
  HEALTH: '/health',
  METRICS: '/metrics',
  PRODUCTS: '/products',
  SEARCH: '/search',
  SENTIMENT: '/sentiment',
  TRENDS: '/trends',
  REDDIT: '/reddit',
  CHAT: '/chat',
  COMPARISON: '/comparison',
  ANALYTICS: '/analytics',
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

// Cache Keys
export const CACHE_KEYS = {
  PRODUCT: 'product',
  PRODUCTS_SEARCH: 'products:search',
  SENTIMENT: 'sentiment',
  TRENDING: 'trending',
  REDDIT: 'reddit',
  REDDIT_POSTS: 'reddit:posts',
  REDDIT_COMMENTS: 'reddit:comments',
  SEARCH: 'search',
  ANALYTICS: 'analytics',
  RATE_LIMIT: 'rate_limit',
} as const;

// Cache TTL (Time To Live) in seconds
export const CACHE_TTL = {
  SHORT: 300, // 5 minutes
  MEDIUM: 1800, // 30 minutes
  LONG: 3600, // 1 hour
  VERY_LONG: 86400, // 24 hours
} as const;

// Rate Limiting
export const RATE_LIMITS = {
  GLOBAL: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // requests per window
  },
  SEARCH: {
    windowMs: 60 * 1000, // 1 minute
    max: 20, // requests per window
  },
  SENTIMENT: {
    windowMs: 60 * 1000, // 1 minute
    max: 10, // requests per window
  },
  REDDIT: {
    windowMs: 60 * 1000, // 1 minute
    max: 30, // requests per window
  },
} as const;

// Reddit API Constants
export const REDDIT_CONFIG = {
  BASE_URL: 'https://www.reddit.com',
  OAUTH_URL: 'https://oauth.reddit.com',
  ENDPOINTS: {
    ACCESS_TOKEN: '/api/v1/access_token',
    SEARCH: '/api/v1/search',
    SUBREDDIT_POSTS: '/r/{subreddit}/hot',
    POST_COMMENTS: '/r/{subreddit}/comments/{post_id}',
  },
  SUBREDDITS: [
    'BuyItForLife',
    'ProductPorn',
    'shutupandtakemymoney',
    'gadgets',
    'technology',
    'reviews',
    'ProductHunters',
    'deals',
    'frugal',
    'BestProducts',
  ],
  RATE_LIMIT: {
    REQUESTS_PER_MINUTE: 60,
    BURST_LIMIT: 10,
  },
} as const;

// Sentiment Analysis Constants
export const SENTIMENT_CONFIG = {
  SCORE_RANGES: {
    VERY_NEGATIVE: [-1, -0.6],
    NEGATIVE: [-0.6, -0.2],
    NEUTRAL: [-0.2, 0.2],
    POSITIVE: [0.2, 0.6],
    VERY_POSITIVE: [0.6, 1],
  },
  CONFIDENCE_THRESHOLD: 0.7,
  BATCH_SIZE: 50,
  MAX_TEXT_LENGTH: 10000,
} as const;

// Product Categories
export const PRODUCT_CATEGORIES = [
  'Electronics',
  'Computers',
  'Smartphones',
  'Gaming',
  'Home & Garden',
  'Kitchen',
  'Appliances',
  'Fashion',
  'Beauty',
  'Health',
  'Sports',
  'Automotive',
  'Books',
  'Music',
  'Movies',
  'Toys',
  'Baby',
  'Pet Supplies',
  'Office',
  'Industrial',
  'Other',
] as const;

// Error Messages
export const ERROR_MESSAGES = {
  VALIDATION_ERROR: 'Validation error',
  NOT_FOUND: 'Resource not found',
  INTERNAL_ERROR: 'Internal server error',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded',
  INVALID_REQUEST: 'Invalid request',
  SERVICE_UNAVAILABLE: 'Service temporarily unavailable',
  REDDIT_API_ERROR: 'Reddit API error',
  SENTIMENT_API_ERROR: 'Sentiment analysis service error',
  CACHE_ERROR: 'Cache service error',
  DATABASE_ERROR: 'Database error',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  PRODUCT_CREATED: 'Product created successfully',
  PRODUCT_UPDATED: 'Product updated successfully',
  PRODUCT_DELETED: 'Product deleted successfully',
  SEARCH_COMPLETED: 'Search completed successfully',
  SENTIMENT_ANALYZED: 'Sentiment analysis completed',
  DATA_FETCHED: 'Data fetched successfully',
} as const;

// Validation Constants
export const VALIDATION_LIMITS = {
  PRODUCT_NAME_MAX_LENGTH: 255,
  PRODUCT_DESCRIPTION_MAX_LENGTH: 5000,
  SEARCH_QUERY_MAX_LENGTH: 255,
  SEARCH_QUERY_MIN_LENGTH: 1,
  SENTIMENT_TEXT_MAX_LENGTH: 10000,
  PAGINATION_MAX_LIMIT: 100,
  PAGINATION_DEFAULT_LIMIT: 20,
  IMAGE_URL_MAX_COUNT: 10,
} as const;

// Socket.io Events
export const SOCKET_EVENTS = {
  CONNECTION: 'connection',
  DISCONNECT: 'disconnect',
  SENTIMENT_UPDATE: 'sentiment_update',
  TRENDING_UPDATE: 'trending_update',
  PRODUCT_UPDATE: 'product_update',
  ERROR: 'error',
  JOIN_ROOM: 'join_room',
  LEAVE_ROOM: 'leave_room',
} as const;

// Monitoring and Metrics
export const METRICS = {
  REQUEST_DURATION: 'http_request_duration_seconds',
  REQUEST_COUNT: 'http_requests_total',
  ACTIVE_CONNECTIONS: 'active_connections',
  CACHE_HITS: 'cache_hits_total',
  CACHE_MISSES: 'cache_misses_total',
  DATABASE_QUERIES: 'database_queries_total',
  REDDIT_API_CALLS: 'reddit_api_calls_total',
  SENTIMENT_ANALYSES: 'sentiment_analyses_total',
} as const;

// File Upload Constants
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_MIME_TYPES: [
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/gif',
  ],
  UPLOAD_PATH: 'uploads',
} as const;
