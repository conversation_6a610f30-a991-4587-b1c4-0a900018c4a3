#!/usr/bin/env node

/**
 * Local Database Setup Script
 * This script helps set up a local development environment without Docker
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 ProductWhisper Backend - Local Database Setup');
console.log('================================================\n');

// Check if .env file exists
const envPath = path.join(__dirname, '..', '.env');
if (!fs.existsSync(envPath)) {
  console.log('❌ .env file not found. Creating from template...');
  
  const envTemplate = `# Development Environment Configuration
# Database
DATABASE_URL=postgresql://postgres:productwhisper123@localhost:5432/productwhisper
REDIS_URL=redis://localhost:6379

# Reddit API (Get from https://www.reddit.com/prefs/apps)
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
REDDIT_USER_AGENT=ProductWhisper/1.0

# Services
SENTIMENT_API_URL=http://localhost:5000
FRONTEND_URL=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Configuration
NODE_ENV=development
PORT=8000
LOG_LEVEL=info

# Security
CORS_ORIGIN=http://localhost:5173
API_PREFIX=/api/v1

# Cache Configuration
CACHE_TTL_SHORT=300
CACHE_TTL_MEDIUM=1800
CACHE_TTL_LONG=3600

# External APIs
REDDIT_RATE_LIMIT_REQUESTS=60
REDDIT_RATE_LIMIT_WINDOW=60000

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9464

# Socket.io
SOCKET_CORS_ORIGIN=http://localhost:5173
`;

  fs.writeFileSync(envPath, envTemplate);
  console.log('✅ Created .env file with default configuration');
  console.log('📝 Please update the Reddit API credentials in .env file\n');
}

// Function to run command and handle errors
function runCommand(command, description) {
  try {
    console.log(`🔄 ${description}...`);
    execSync(command, { stdio: 'inherit', cwd: path.join(__dirname, '..') });
    console.log(`✅ ${description} completed\n`);
    return true;
  } catch (error) {
    console.log(`❌ ${description} failed:`, error.message);
    return false;
  }
}

// Function to check if service is running
function checkService(command, serviceName) {
  try {
    execSync(command, { stdio: 'ignore' });
    console.log(`✅ ${serviceName} is running`);
    return true;
  } catch (error) {
    console.log(`❌ ${serviceName} is not running`);
    return false;
  }
}

console.log('🔍 Checking prerequisites...');

// Check Node.js version
const nodeVersion = process.version;
console.log(`📦 Node.js version: ${nodeVersion}`);

// Check if PostgreSQL is available
const pgAvailable = checkService('pg_isready', 'PostgreSQL');
const redisAvailable = checkService('redis-cli ping', 'Redis');

console.log('\n📋 Setup Options:');
console.log('1. Full setup with database (requires PostgreSQL and Redis)');
console.log('2. Development setup without database (API structure only)');
console.log('3. Docker setup (requires Docker)');

// For now, let's proceed with option 2 - development setup
console.log('\n🛠️  Proceeding with development setup...\n');

// Install dependencies
if (!runCommand('npm install', 'Installing dependencies')) {
  process.exit(1);
}

// Generate Prisma client
if (!runCommand('npm run db:generate', 'Generating Prisma client')) {
  console.log('⚠️  Prisma client generation failed, but continuing...');
}

// Build the project
if (!runCommand('npm run build', 'Building TypeScript project')) {
  console.log('❌ Build failed. Please check for TypeScript errors.');
  process.exit(1);
}

// Run basic tests
if (!runCommand('npm test -- --testPathPattern=simple.test.ts --passWithNoTests', 'Running basic tests')) {
  console.log('⚠️  Some tests failed, but setup can continue...');
}

console.log('🎉 Development setup completed!\n');

console.log('📋 Next Steps:');
console.log('==============');

if (pgAvailable && redisAvailable) {
  console.log('✅ Database services detected!');
  console.log('   Run: npm run db:migrate    # Set up database schema');
  console.log('   Run: npm run db:seed       # Add sample data');
  console.log('   Run: npm run dev           # Start development server');
} else {
  console.log('📝 To set up database services:');
  console.log('   Option 1 - Docker: docker compose up -d postgres redis');
  console.log('   Option 2 - Local: Install PostgreSQL and Redis locally');
  console.log('   Option 3 - Cloud: Use cloud database services');
}

console.log('\n🚀 Development Commands:');
console.log('   npm run dev              # Start development server');
console.log('   npm run build            # Build for production');
console.log('   npm test                 # Run tests');
console.log('   npm run db:studio        # Open Prisma Studio');

console.log('\n📚 Documentation:');
console.log('   DEVELOPMENT.md           # Development guide');
console.log('   IMPLEMENTATION_SUMMARY.md # Implementation details');

console.log('\n🌐 API Endpoints (when server is running):');
console.log('   http://localhost:8000/health           # Health check');
console.log('   http://localhost:8000/api/v1/products  # Products API');
console.log('   http://localhost:8000/api/v1/search    # Search API');
console.log('   http://localhost:8000/api/v1/sentiment # Sentiment API');
console.log('   http://localhost:8000/api/v1/reddit    # Reddit API');

console.log('\n✨ Your ProductWhisper backend is ready for development!');
