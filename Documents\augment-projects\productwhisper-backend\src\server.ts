import Fastify, { FastifyInstance } from 'fastify';
import { serverConfig, corsConfig, rateLimitConfig } from '@/config';
import { setupPlugins } from '@/api/plugins';
import { setupRoutes } from '@/api/routes';
import { errorHandler } from '@/api/middleware/errorHandler';
import { requestLogger } from '@/api/middleware/requestLogger';
import { prisma } from '@/infrastructure/database/prisma';
import { redisCache } from '@/infrastructure/cache/redis';

// Create Fastify instance
const fastify: FastifyInstance = Fastify({
  logger: {
    level: serverConfig.logLevel,
    transport: serverConfig.nodeEnv === 'development' ? {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'HH:MM:ss Z',
        ignore: 'pid,hostname',
      },
    } : undefined,
  },
  trustProxy: true,
  disableRequestLogging: false,
  requestIdHeader: 'x-request-id',
  requestIdLogLabel: 'requestId',
});

// Global error handler
fastify.setErrorHandler(errorHandler);

// Setup plugins and middleware
async function setupServer() {
  try {
    // Register plugins
    await setupPlugins(fastify);

    // Register middleware
    await fastify.register(requestLogger);

    // Register routes
    await setupRoutes(fastify);

    // Health check endpoint
    fastify.get('/health', async (request, reply) => {
      const dbHealth = await prisma.healthCheck();
      const cacheHealth = await redisCache.healthCheck();

      const health = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: dbHealth ? 'connected' : 'disconnected',
        cache: cacheHealth ? 'connected' : 'disconnected',
        memory: process.memoryUsage(),
        version: process.env.npm_package_version || '1.0.0',
      };

      const statusCode = dbHealth && cacheHealth ? 200 : 503;
      
      return reply.status(statusCode).send(health);
    });

    // Ready check endpoint
    fastify.get('/ready', async (request, reply) => {
      const dbHealth = await prisma.healthCheck();
      
      if (!dbHealth) {
        return reply.status(503).send({
          status: 'not ready',
          reason: 'Database not available',
        });
      }

      return reply.send({
        status: 'ready',
        timestamp: new Date().toISOString(),
      });
    });

    // Metrics endpoint (if enabled)
    if (serverConfig.nodeEnv === 'production') {
      fastify.get('/metrics', async (request, reply) => {
        // Prometheus metrics would be implemented here
        return reply.send('# Metrics endpoint - implementation pending');
      });
    }

    return fastify;
  } catch (error) {
    fastify.log.error('Failed to setup server:', error);
    throw error;
  }
}

// Start server
async function start() {
  try {
    const server = await setupServer();
    
    const address = await server.listen({
      port: serverConfig.port,
      host: '0.0.0.0',
    });

    console.log(`
🚀 ProductWhisper Backend Server Started!
📍 Server running at: ${address}
🌍 Environment: ${serverConfig.nodeEnv}
📊 Log Level: ${serverConfig.logLevel}
🔗 API Prefix: ${serverConfig.apiPrefix}
    `);

    // Graceful shutdown
    const gracefulShutdown = async (signal: string) => {
      console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);
      
      try {
        await server.close();
        await prisma.onModuleDestroy();
        await redisCache.disconnect();
        console.log('✅ Server shut down successfully');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('❌ Uncaught Exception:', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  start();
}

export { fastify, setupServer, start };
export default fastify;
