import { FastifyInstance } from 'fastify';
import { SentimentController } from '@/api/controllers/SentimentController';
import { asyncHandler } from '@/shared/errors';

const sentimentController = new SentimentController();

export async function sentimentRoutes(fastify: FastifyInstance) {
  // Analyze sentiment of text
  fastify.post('/analyze', {
    schema: {
      tags: ['Sentiment'],
      summary: 'Analyze sentiment of text',
      description: 'Perform sentiment analysis on provided text',
      body: {
        type: 'object',
        required: ['text'],
        properties: {
          text: { type: 'string', minLength: 1, maxLength: 5000 },
          language: { type: 'string', description: 'Language of the text' },
          context: { type: 'string', enum: ['product_review', 'social_media', 'comment', 'general'], default: 'general' },
        },
      },
    },
  }, asyncHandler(sentimentController.analyzeSentiment.bind(sentimentController)));

  // Batch sentiment analysis
  fastify.post('/analyze/batch', {
    schema: {
      tags: ['Sentiment'],
      summary: 'Batch sentiment analysis',
      description: 'Analyze sentiment for multiple texts',
      body: {
        type: 'object',
        required: ['texts'],
        properties: {
          texts: {
            type: 'array',
            items: { type: 'string', minLength: 1, maxLength: 5000 },
            minItems: 1,
            maxItems: 100,
          },
          language: { type: 'string', description: 'Language of the texts' },
          context: { type: 'string', enum: ['product_review', 'social_media', 'comment', 'general'], default: 'general' },
        },
      },
    },
  }, asyncHandler(sentimentController.analyzeBatchSentiment.bind(sentimentController)));

  // Analyze product mentions sentiment
  fastify.post('/product/analyze', {
    schema: {
      tags: ['Sentiment'],
      summary: 'Analyze product mentions sentiment',
      description: 'Analyze sentiment for all mentions of a product',
      body: {
        type: 'object',
        required: ['productId'],
        properties: {
          productId: { type: 'string', format: 'uuid' },
        },
      },
    },
  }, asyncHandler(sentimentController.analyzeProductMentions.bind(sentimentController)));

  // Get sentiment trends for product
  fastify.get('/trends', {
    schema: {
      tags: ['Sentiment'],
      summary: 'Get sentiment trends',
      description: 'Get sentiment trends for a product over time',
      querystring: {
        type: 'object',
        required: ['productId'],
        properties: {
          productId: { type: 'string', format: 'uuid' },
          timeframe: { type: 'string', enum: ['day', 'week', 'month'], default: 'week' },
        },
      },
    },
  }, asyncHandler(sentimentController.getSentimentTrends.bind(sentimentController)));

  // Get product sentiment summary
  fastify.get('/product/:productId/summary', {
    schema: {
      tags: ['Sentiment'],
      summary: 'Get product sentiment summary',
      description: 'Get comprehensive sentiment summary for a product',
      params: {
        type: 'object',
        required: ['productId'],
        properties: {
          productId: { type: 'string', format: 'uuid' },
        },
      },
    },
  }, asyncHandler(sentimentController.getProductSentimentSummary.bind(sentimentController)));

  // Compare sentiment between products
  fastify.post('/compare', {
    schema: {
      tags: ['Sentiment'],
      summary: 'Compare product sentiments',
      description: 'Compare sentiment analysis between multiple products',
      body: {
        type: 'object',
        required: ['productIds'],
        properties: {
          productIds: {
            type: 'array',
            items: { type: 'string', format: 'uuid' },
            minItems: 2,
            maxItems: 10,
          },
          timeframe: { type: 'string', enum: ['day', 'week', 'month'], default: 'week' },
        },
      },
    },
  }, asyncHandler(sentimentController.compareSentiments.bind(sentimentController)));

  // Get sentiment insights
  fastify.get('/insights/:productId', {
    schema: {
      tags: ['Sentiment'],
      summary: 'Get sentiment insights',
      description: 'Get AI-generated insights about product sentiment',
      params: {
        type: 'object',
        required: ['productId'],
        properties: {
          productId: { type: 'string', format: 'uuid' },
        },
      },
      querystring: {
        type: 'object',
        properties: {
          timeframe: { type: 'string', enum: ['day', 'week', 'month'], default: 'week' },
        },
      },
    },
  }, asyncHandler(sentimentController.getSentimentInsights.bind(sentimentController)));

  // Get sentiment statistics
  fastify.get('/stats', {
    schema: {
      tags: ['Sentiment'],
      summary: 'Get sentiment statistics',
      description: 'Get overall sentiment analysis statistics',
      querystring: {
        type: 'object',
        properties: {
          timeframe: { type: 'string', enum: ['day', 'week', 'month'], default: 'week' },
          productId: { type: 'string', format: 'uuid', description: 'Filter by product ID' },
        },
      },
    },
  }, asyncHandler(sentimentController.getSentimentStats.bind(sentimentController)));

  // Sentiment service health check
  fastify.get('/health', {
    schema: {
      tags: ['Sentiment'],
      summary: 'Sentiment service health check',
      description: 'Check the health status of sentiment analysis service',
    },
  }, asyncHandler(sentimentController.getSentimentHealth.bind(sentimentController)));
}
