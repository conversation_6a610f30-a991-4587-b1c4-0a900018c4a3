import { ProductEntity, ProductWithSentiment, SentimentScoreEntity, ProductMentionEntity } from '@/core/entities/Product';
import { ProductSearchFilters, ProductSearchSort, PaginationParams } from '@/shared/types';

export interface ProductRepository {
  // Basic CRUD operations
  create(product: Omit<ProductEntity, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProductEntity>;
  findById(id: string): Promise<ProductEntity | null>;
  findByIds(ids: string[]): Promise<ProductEntity[]>;
  update(id: string, data: Partial<ProductEntity>): Promise<ProductEntity>;
  delete(id: string): Promise<boolean>;

  // Search and filtering
  search(
    filters: ProductSearchFilters,
    sort: ProductSearchSort,
    pagination: PaginationParams
  ): Promise<{ products: ProductEntity[]; total: number }>;

  findByName(name: string): Promise<ProductEntity | null>;
  findByExternalId(platform: string, externalId: string): Promise<ProductEntity | null>;
  findByCategory(category: string, pagination: PaginationParams): Promise<{ products: ProductEntity[]; total: number }>;
  findByBrand(brand: string, pagination: PaginationParams): Promise<{ products: ProductEntity[]; total: number }>;

  // Advanced queries
  findWithSentiment(id: string): Promise<ProductWithSentiment | null>;
  findTrending(limit: number): Promise<ProductWithSentiment[]>;
  findRecentlyAdded(limit: number): Promise<ProductEntity[]>;
  findPopular(limit: number): Promise<ProductWithSentiment[]>;

  // Bulk operations
  createMany(products: Omit<ProductEntity, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<ProductEntity[]>;
  updateMany(updates: { id: string; data: Partial<ProductEntity> }[]): Promise<ProductEntity[]>;

  // Statistics
  count(): Promise<number>;
  countByCategory(): Promise<{ category: string; count: number }[]>;
  countByBrand(): Promise<{ brand: string; count: number }[]>;

  // Sentiment-related operations
  addSentimentScore(sentimentScore: Omit<SentimentScoreEntity, 'id' | 'lastUpdated'>): Promise<SentimentScoreEntity>;
  updateSentimentScore(productId: string, source: string, data: Partial<SentimentScoreEntity>): Promise<SentimentScoreEntity>;
  getSentimentScores(productId: string): Promise<SentimentScoreEntity[]>;

  // Mention-related operations
  addMention(mention: Omit<ProductMentionEntity, 'id' | 'processedAt'>): Promise<ProductMentionEntity>;
  getMentions(productId: string, pagination: PaginationParams): Promise<{ mentions: ProductMentionEntity[]; total: number }>;
  getMentionsBySource(productId: string, source: string, pagination: PaginationParams): Promise<{ mentions: ProductMentionEntity[]; total: number }>;
  findMentionBySourceId(source: string, sourceId: string): Promise<ProductMentionEntity | null>;
}

import { PrismaClient } from '@prisma/client';
import { prisma } from '@/infrastructure/database/prisma';
import { DatabaseError, NotFoundError } from '@/shared/errors';
import { PlatformSource } from '@/shared/types';

export class PrismaProductRepository implements ProductRepository {
  constructor(private db: PrismaClient = prisma) { }

  async create(productData: Omit<ProductEntity, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProductEntity> {
    try {
      const product = await this.db.product.create({
        data: {
          name: productData.name,
          description: productData.description,
          category: productData.category,
          brand: productData.brand,
          imageUrls: productData.imageUrls,
          externalIds: productData.externalIds as any,
          metadata: productData.metadata as any,
        },
      });

      return this.mapToEntity(product);
    } catch (error) {
      throw new DatabaseError('Failed to create product', { error });
    }
  }

  async findById(id: string): Promise<ProductEntity | null> {
    try {
      const product = await this.db.product.findUnique({
        where: { id },
      });

      return product ? this.mapToEntity(product) : null;
    } catch (error) {
      throw new DatabaseError('Failed to find product by ID', { error });
    }
  }

  async findByIds(ids: string[]): Promise<ProductEntity[]> {
    try {
      const products = await this.db.product.findMany({
        where: { id: { in: ids } },
      });

      return products.map(this.mapToEntity);
    } catch (error) {
      throw new DatabaseError('Failed to find products by IDs', { error });
    }
  }

  async update(id: string, data: Partial<ProductEntity>): Promise<ProductEntity> {
    try {
      const product = await this.db.product.update({
        where: { id },
        data: {
          ...(data.name && { name: data.name }),
          ...(data.description !== undefined && { description: data.description }),
          ...(data.category !== undefined && { category: data.category }),
          ...(data.brand !== undefined && { brand: data.brand }),
          ...(data.imageUrls && { imageUrls: data.imageUrls }),
          ...(data.externalIds && { externalIds: data.externalIds as any }),
          ...(data.metadata && { metadata: data.metadata as any }),
        },
      });

      return this.mapToEntity(product);
    } catch (error) {
      if (error instanceof Error && error.message.includes('Record to update not found')) {
        throw new NotFoundError('Product');
      }
      throw new DatabaseError('Failed to update product', { error });
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      await this.db.product.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      if (error instanceof Error && error.message.includes('Record to delete does not exist')) {
        return false;
      }
      throw new DatabaseError('Failed to delete product', { error });
    }
  }

  async search(
    filters: ProductSearchFilters,
    sort: ProductSearchSort,
    pagination: PaginationParams
  ): Promise<{ products: ProductEntity[]; total: number }> {
    try {
      const where: any = {};

      // Apply filters
      if (filters.category) {
        where.category = { contains: filters.category, mode: 'insensitive' as any };
      }

      if (filters.brand) {
        where.brand = { contains: filters.brand, mode: 'insensitive' as any };
      }

      if (filters.minPrice || filters.maxPrice) {
        where.metadata = {
          path: ['price'],
          ...(filters.minPrice && { gte: filters.minPrice }),
          ...(filters.maxPrice && { lte: filters.maxPrice }),
        };
      }

      if (filters.tags && filters.tags.length > 0) {
        where.metadata = {
          path: ['tags'],
          array_contains: filters.tags,
        };
      }

      if (filters.dateFrom || filters.dateTo) {
        where.createdAt = {
          ...(filters.dateFrom && { gte: filters.dateFrom }),
          ...(filters.dateTo && { lte: filters.dateTo }),
        };
      }

      // Apply sorting
      const orderBy: any = {};
      switch (sort.field) {
        case 'name':
          orderBy.name = sort.order;
          break;
        case 'createdAt':
          orderBy.createdAt = sort.order;
          break;
        case 'updatedAt':
          orderBy.updatedAt = sort.order;
          break;
        default:
          orderBy.createdAt = 'desc';
      }

      const [products, total] = await Promise.all([
        this.db.product.findMany({
          where,
          orderBy,
          skip: ((pagination.page || 1) - 1) * (pagination.limit || 20),
          take: pagination.limit || 20,
        }),
        this.db.product.count({ where }),
      ]);

      return {
        products: products.map(this.mapToEntity),
        total,
      };
    } catch (error) {
      throw new DatabaseError('Failed to search products', { error });
    }
  }

  async findByName(name: string): Promise<ProductEntity | null> {
    try {
      const product = await this.db.product.findFirst({
        where: { name: { equals: name, mode: 'insensitive' as any } },
      });

      return product ? this.mapToEntity(product) : null;
    } catch (error) {
      throw new DatabaseError('Failed to find product by name', { error });
    }
  }

  async findByExternalId(platform: string, externalId: string): Promise<ProductEntity | null> {
    try {
      const product = await this.db.product.findFirst({
        where: {
          externalIds: {
            path: [platform],
            equals: externalId,
          },
        },
      });

      return product ? this.mapToEntity(product) : null;
    } catch (error) {
      throw new DatabaseError('Failed to find product by external ID', { error });
    }
  }

  async findByCategory(category: string, pagination: PaginationParams): Promise<{ products: ProductEntity[]; total: number }> {
    try {
      const where = { category: { contains: category, mode: 'insensitive' as any } };

      const [products, total] = await Promise.all([
        this.db.product.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip: ((pagination.page || 1) - 1) * (pagination.limit || 20),
          take: pagination.limit || 20,
        }),
        this.db.product.count({ where }),
      ]);

      return {
        products: products.map(this.mapToEntity),
        total,
      };
    } catch (error) {
      throw new DatabaseError('Failed to find products by category', { error });
    }
  }

  async findByBrand(brand: string, pagination: PaginationParams): Promise<{ products: ProductEntity[]; total: number }> {
    try {
      const where = { brand: { contains: brand, mode: 'insensitive' as any } };

      const [products, total] = await Promise.all([
        this.db.product.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip: ((pagination.page || 1) - 1) * (pagination.limit || 20),
          take: pagination.limit || 20,
        }),
        this.db.product.count({ where }),
      ]);

      return {
        products: products.map(this.mapToEntity),
        total,
      };
    } catch (error) {
      throw new DatabaseError('Failed to find products by brand', { error });
    }
  }

  // Helper method to map Prisma model to entity
  private mapToEntity(product: any): ProductEntity {
    return {
      id: product.id,
      name: product.name,
      description: product.description,
      category: product.category,
      brand: product.brand,
      imageUrls: product.imageUrls,
      externalIds: product.externalIds,
      metadata: product.metadata,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
    };
  }

  async findWithSentiment(id: string): Promise<ProductWithSentiment | null> {
    try {
      const product = await this.db.product.findUnique({
        where: { id },
        include: {
          sentimentScores: true,
          mentions: {
            take: 10,
            orderBy: { createdAt: 'desc' }
          }
        }
      });

      if (!product) return null;

      const sentimentScores = product.sentimentScores.map(score => ({
        id: score.id,
        productId: score.productId,
        source: score.source as PlatformSource,
        overallScore: Number(score.overallScore),
        positiveScore: score.positiveScore ? Number(score.positiveScore) : undefined,
        negativeScore: score.negativeScore ? Number(score.negativeScore) : undefined,
        neutralScore: score.neutralScore ? Number(score.neutralScore) : undefined,
        confidenceScore: score.confidenceScore ? Number(score.confidenceScore) : undefined,
        sampleSize: score.sampleSize,
        lastUpdated: score.lastUpdated
      }));

      const mentions = product.mentions.map(mention => ({
        id: mention.id,
        productId: mention.productId,
        source: mention.source as PlatformSource,
        sourceId: mention.sourceId,
        content: mention.content,
        sentimentScore: mention.sentimentScore ? Number(mention.sentimentScore) : undefined,
        engagementMetrics: mention.engagementMetrics as Record<string, any>,
        url: mention.url || undefined,
        authorInfo: mention.authorInfo as Record<string, any>,
        createdAt: mention.createdAt || undefined,
        processedAt: mention.processedAt
      }));

      const averageSentiment = sentimentScores.length > 0
        ? sentimentScores.reduce((sum, score) => sum + score.overallScore, 0) / sentimentScores.length
        : 0;

      return {
        ...this.mapToEntity(product),
        sentimentScores,
        mentions,
        averageSentiment,
        totalMentions: mentions.length,
        trendScore: averageSentiment * mentions.length
      };
    } catch (error) {
      throw new DatabaseError('Failed to find product with sentiment', { error });
    }
  }

  async findTrending(limit: number): Promise<ProductWithSentiment[]> {
    try {
      // This would use the trending_products view in production
      const products = await this.db.product.findMany({
        include: {
          sentimentScores: true,
          mentions: {
            where: {
              createdAt: {
                gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
              }
            }
          }
        },
        take: limit
      });

      return products.map(product => {
        const sentimentScores = product.sentimentScores.map(score => ({
          id: score.id,
          productId: score.productId,
          source: score.source as PlatformSource,
          overallScore: Number(score.overallScore),
          positiveScore: score.positiveScore ? Number(score.positiveScore) : undefined,
          negativeScore: score.negativeScore ? Number(score.negativeScore) : undefined,
          neutralScore: score.neutralScore ? Number(score.neutralScore) : undefined,
          confidenceScore: score.confidenceScore ? Number(score.confidenceScore) : undefined,
          sampleSize: score.sampleSize,
          lastUpdated: score.lastUpdated
        }));

        const mentions = product.mentions.map(mention => ({
          id: mention.id,
          productId: mention.productId,
          source: mention.source as PlatformSource,
          sourceId: mention.sourceId,
          content: mention.content,
          sentimentScore: mention.sentimentScore ? Number(mention.sentimentScore) : undefined,
          engagementMetrics: mention.engagementMetrics as Record<string, any>,
          url: mention.url || undefined,
          authorInfo: mention.authorInfo as Record<string, any>,
          createdAt: mention.createdAt || undefined,
          processedAt: mention.processedAt
        }));

        const averageSentiment = sentimentScores.length > 0
          ? sentimentScores.reduce((sum, score) => sum + score.overallScore, 0) / sentimentScores.length
          : 0;

        return {
          ...this.mapToEntity(product),
          sentimentScores,
          mentions,
          averageSentiment,
          totalMentions: mentions.length,
          trendScore: averageSentiment * mentions.length
        };
      }).sort((a, b) => (b.trendScore || 0) - (a.trendScore || 0));
    } catch (error) {
      throw new DatabaseError('Failed to find trending products', { error });
    }
  }

  async findRecentlyAdded(limit: number): Promise<ProductEntity[]> {
    try {
      const products = await this.db.product.findMany({
        orderBy: { createdAt: 'desc' },
        take: limit,
      });

      return products.map(this.mapToEntity);
    } catch (error) {
      throw new DatabaseError('Failed to find recently added products', { error });
    }
  }

  async findPopular(limit: number): Promise<ProductWithSentiment[]> {
    try {
      const products = await this.db.product.findMany({
        include: {
          sentimentScores: true,
          mentions: true
        },
        take: limit * 2 // Get more to filter and sort
      });

      return products.map(product => {
        const sentimentScores = product.sentimentScores.map(score => ({
          id: score.id,
          productId: score.productId,
          source: score.source as PlatformSource,
          overallScore: Number(score.overallScore),
          positiveScore: score.positiveScore ? Number(score.positiveScore) : undefined,
          negativeScore: score.negativeScore ? Number(score.negativeScore) : undefined,
          neutralScore: score.neutralScore ? Number(score.neutralScore) : undefined,
          confidenceScore: score.confidenceScore ? Number(score.confidenceScore) : undefined,
          sampleSize: score.sampleSize,
          lastUpdated: score.lastUpdated
        }));

        const mentions = product.mentions.map(mention => ({
          id: mention.id,
          productId: mention.productId,
          source: mention.source as PlatformSource,
          sourceId: mention.sourceId,
          content: mention.content,
          sentimentScore: mention.sentimentScore ? Number(mention.sentimentScore) : undefined,
          engagementMetrics: mention.engagementMetrics as Record<string, any>,
          url: mention.url || undefined,
          authorInfo: mention.authorInfo as Record<string, any>,
          createdAt: mention.createdAt || undefined,
          processedAt: mention.processedAt
        }));

        const averageSentiment = sentimentScores.length > 0
          ? sentimentScores.reduce((sum, score) => sum + score.overallScore, 0) / sentimentScores.length
          : 0;

        return {
          ...this.mapToEntity(product),
          sentimentScores,
          mentions,
          averageSentiment,
          totalMentions: mentions.length,
          trendScore: mentions.length * (1 + averageSentiment)
        };
      })
        .sort((a, b) => (b.totalMentions || 0) - (a.totalMentions || 0))
        .slice(0, limit);
    } catch (error) {
      throw new DatabaseError('Failed to find popular products', { error });
    }
  }

  async createMany(products: Omit<ProductEntity, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<ProductEntity[]> {
    try {
      const result = await this.db.product.createMany({
        data: products.map(product => ({
          name: product.name,
          description: product.description,
          category: product.category,
          brand: product.brand,
          imageUrls: product.imageUrls,
          externalIds: product.externalIds as any,
          metadata: product.metadata as any,
        })),
        skipDuplicates: true,
      });

      // Since createMany doesn't return the created records, we need to fetch them
      // This is a limitation of Prisma's createMany
      const createdProducts = await this.db.product.findMany({
        orderBy: { createdAt: 'desc' },
        take: result.count,
      });

      return createdProducts.map(this.mapToEntity);
    } catch (error) {
      throw new DatabaseError('Failed to create multiple products', { error });
    }
  }

  async updateMany(updates: { id: string; data: Partial<ProductEntity> }[]): Promise<ProductEntity[]> {
    try {
      const results = await Promise.all(
        updates.map(({ id, data }) => this.update(id, data))
      );
      return results;
    } catch (error) {
      throw new DatabaseError('Failed to update multiple products', { error });
    }
  }

  async count(): Promise<number> {
    try {
      return await this.db.product.count();
    } catch (error) {
      throw new DatabaseError('Failed to count products', { error });
    }
  }

  async countByCategory(): Promise<{ category: string; count: number }[]> {
    try {
      const result = await this.db.product.groupBy({
        by: ['category'],
        _count: { category: true },
        where: { category: { not: null } },
      });

      return result.map(item => ({
        category: item.category || 'Unknown',
        count: item._count.category,
      }));
    } catch (error) {
      throw new DatabaseError('Failed to count products by category', { error });
    }
  }

  async countByBrand(): Promise<{ brand: string; count: number }[]> {
    try {
      const result = await this.db.product.groupBy({
        by: ['brand'],
        _count: { brand: true },
        where: { brand: { not: null } },
      });

      return result.map(item => ({
        brand: item.brand || 'Unknown',
        count: item._count.brand,
      }));
    } catch (error) {
      throw new DatabaseError('Failed to count products by brand', { error });
    }
  }

  // Sentiment and mention methods
  async addSentimentScore(sentimentScore: Omit<SentimentScoreEntity, 'id' | 'lastUpdated'>): Promise<SentimentScoreEntity> {
    try {
      const score = await this.db.sentimentScore.create({
        data: {
          productId: sentimentScore.productId,
          source: sentimentScore.source,
          overallScore: sentimentScore.overallScore,
          positiveScore: sentimentScore.positiveScore,
          negativeScore: sentimentScore.negativeScore,
          neutralScore: sentimentScore.neutralScore,
          confidenceScore: sentimentScore.confidenceScore,
          sampleSize: sentimentScore.sampleSize
        }
      });

      return {
        id: score.id,
        productId: score.productId,
        source: score.source as PlatformSource,
        overallScore: Number(score.overallScore),
        positiveScore: score.positiveScore ? Number(score.positiveScore) : undefined,
        negativeScore: score.negativeScore ? Number(score.negativeScore) : undefined,
        neutralScore: score.neutralScore ? Number(score.neutralScore) : undefined,
        confidenceScore: score.confidenceScore ? Number(score.confidenceScore) : undefined,
        sampleSize: score.sampleSize,
        lastUpdated: score.lastUpdated
      };
    } catch (error) {
      throw new DatabaseError('Failed to add sentiment score', { error });
    }
  }

  async updateSentimentScore(productId: string, source: string, data: Partial<SentimentScoreEntity>): Promise<SentimentScoreEntity> {
    try {
      const score = await this.db.sentimentScore.update({
        where: {
          productId_source: {
            productId,
            source: source as any
          }
        },
        data: {
          ...(data.overallScore !== undefined && { overallScore: data.overallScore }),
          ...(data.positiveScore !== undefined && { positiveScore: data.positiveScore }),
          ...(data.negativeScore !== undefined && { negativeScore: data.negativeScore }),
          ...(data.neutralScore !== undefined && { neutralScore: data.neutralScore }),
          ...(data.confidenceScore !== undefined && { confidenceScore: data.confidenceScore }),
          ...(data.sampleSize !== undefined && { sampleSize: data.sampleSize })
        }
      });

      return {
        id: score.id,
        productId: score.productId,
        source: score.source as PlatformSource,
        overallScore: Number(score.overallScore),
        positiveScore: score.positiveScore ? Number(score.positiveScore) : undefined,
        negativeScore: score.negativeScore ? Number(score.negativeScore) : undefined,
        neutralScore: score.neutralScore ? Number(score.neutralScore) : undefined,
        confidenceScore: score.confidenceScore ? Number(score.confidenceScore) : undefined,
        sampleSize: score.sampleSize,
        lastUpdated: score.lastUpdated
      };
    } catch (error) {
      throw new DatabaseError('Failed to update sentiment score', { error });
    }
  }

  async getSentimentScores(productId: string): Promise<SentimentScoreEntity[]> {
    try {
      const scores = await this.db.sentimentScore.findMany({
        where: { productId }
      });

      return scores.map(score => ({
        id: score.id,
        productId: score.productId,
        source: score.source as PlatformSource,
        overallScore: Number(score.overallScore),
        positiveScore: score.positiveScore ? Number(score.positiveScore) : undefined,
        negativeScore: score.negativeScore ? Number(score.negativeScore) : undefined,
        neutralScore: score.neutralScore ? Number(score.neutralScore) : undefined,
        confidenceScore: score.confidenceScore ? Number(score.confidenceScore) : undefined,
        sampleSize: score.sampleSize,
        lastUpdated: score.lastUpdated
      }));
    } catch (error) {
      throw new DatabaseError('Failed to get sentiment scores', { error });
    }
  }

  async addMention(mention: Omit<ProductMentionEntity, 'id' | 'processedAt'>): Promise<ProductMentionEntity> {
    try {
      const mentionRecord = await this.db.productMention.create({
        data: {
          productId: mention.productId,
          source: mention.source,
          sourceId: mention.sourceId,
          content: mention.content,
          sentimentScore: mention.sentimentScore,
          engagementMetrics: mention.engagementMetrics,
          url: mention.url,
          authorInfo: mention.authorInfo,
          createdAt: mention.createdAt
        }
      });

      return {
        id: mentionRecord.id,
        productId: mentionRecord.productId,
        source: mentionRecord.source as PlatformSource,
        sourceId: mentionRecord.sourceId,
        content: mentionRecord.content,
        sentimentScore: mentionRecord.sentimentScore ? Number(mentionRecord.sentimentScore) : undefined,
        engagementMetrics: mentionRecord.engagementMetrics as Record<string, any>,
        url: mentionRecord.url || undefined,
        authorInfo: mentionRecord.authorInfo as Record<string, any>,
        createdAt: mentionRecord.createdAt || undefined,
        processedAt: mentionRecord.processedAt
      };
    } catch (error) {
      throw new DatabaseError('Failed to add mention', { error });
    }
  }

  async getMentions(productId: string, pagination: PaginationParams): Promise<{ mentions: ProductMentionEntity[]; total: number }> {
    try {
      const [mentions, total] = await Promise.all([
        this.db.productMention.findMany({
          where: { productId },
          orderBy: { createdAt: 'desc' },
          skip: ((pagination.page || 1) - 1) * (pagination.limit || 20),
          take: pagination.limit || 20
        }),
        this.db.productMention.count({ where: { productId } })
      ]);

      return {
        mentions: mentions.map(mention => ({
          id: mention.id,
          productId: mention.productId,
          source: mention.source as PlatformSource,
          sourceId: mention.sourceId,
          content: mention.content,
          sentimentScore: mention.sentimentScore ? Number(mention.sentimentScore) : undefined,
          engagementMetrics: mention.engagementMetrics as Record<string, any>,
          url: mention.url || undefined,
          authorInfo: mention.authorInfo as Record<string, any>,
          createdAt: mention.createdAt || undefined,
          processedAt: mention.processedAt
        })),
        total
      };
    } catch (error) {
      throw new DatabaseError('Failed to get mentions', { error });
    }
  }

  async getMentionsBySource(productId: string, source: string, pagination: PaginationParams): Promise<{ mentions: ProductMentionEntity[]; total: number }> {
    try {
      const [mentions, total] = await Promise.all([
        this.db.productMention.findMany({
          where: {
            productId,
            source: source as any
          },
          orderBy: { createdAt: 'desc' },
          skip: ((pagination.page || 1) - 1) * (pagination.limit || 20),
          take: pagination.limit || 20
        }),
        this.db.productMention.count({
          where: {
            productId,
            source: source as any
          }
        })
      ]);

      return {
        mentions: mentions.map(mention => ({
          id: mention.id,
          productId: mention.productId,
          source: mention.source as PlatformSource,
          sourceId: mention.sourceId,
          content: mention.content,
          sentimentScore: mention.sentimentScore ? Number(mention.sentimentScore) : undefined,
          engagementMetrics: mention.engagementMetrics as Record<string, any>,
          url: mention.url || undefined,
          authorInfo: mention.authorInfo as Record<string, any>,
          createdAt: mention.createdAt || undefined,
          processedAt: mention.processedAt
        })),
        total
      };
    } catch (error) {
      throw new DatabaseError('Failed to get mentions by source', { error });
    }
  }

  async findMentionBySourceId(source: string, sourceId: string): Promise<ProductMentionEntity | null> {
    try {
      const mention = await this.db.productMention.findUnique({
        where: {
          source_sourceId: {
            source: source as any,
            sourceId
          }
        }
      });

      if (!mention) return null;

      return {
        id: mention.id,
        productId: mention.productId,
        source: mention.source as PlatformSource,
        sourceId: mention.sourceId,
        content: mention.content,
        sentimentScore: mention.sentimentScore ? Number(mention.sentimentScore) : undefined,
        engagementMetrics: mention.engagementMetrics as Record<string, any>,
        url: mention.url || undefined,
        authorInfo: mention.authorInfo as Record<string, any>,
        createdAt: mention.createdAt || undefined,
        processedAt: mention.processedAt
      };
    } catch (error) {
      throw new DatabaseError('Failed to find mention by source ID', { error });
    }
  }
}
