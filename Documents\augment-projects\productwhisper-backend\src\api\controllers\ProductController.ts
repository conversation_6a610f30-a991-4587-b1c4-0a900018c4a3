import { FastifyRequest, FastifyReply } from 'fastify';
import { ProductService } from '@/core/services/ProductService';
import { ProductSearchSchema, PaginationSchema, ProductCreateSchema } from '@/shared/types';
import { ValidationError, NotFoundError } from '@/shared/errors';
import { SUCCESS_MESSAGES } from '@/shared/constants';
import { createSuccessResponse, createPaginatedResponse } from '@/shared/utils';

export class ProductController {
  private productService: ProductService;

  constructor() {
    this.productService = new ProductService();
  }

  async searchProducts(request: FastifyRequest, reply: FastifyReply) {
    // Validate query parameters
    const queryValidation = ProductSearchSchema.merge(PaginationSchema).safeParse(request.query);

    if (!queryValidation.success) {
      throw new ValidationError('Invalid search parameters', queryValidation.error.errors);
    }

    const { q, category, brand, minPrice, maxPrice, minRating, maxRating, tags, source, sortBy, sortOrder, page, limit } = queryValidation.data;

    // Build filters
    const filters = {
      ...(category && { category }),
      ...(brand && { brand }),
      ...(minPrice && { minPrice }),
      ...(maxPrice && { maxPrice }),
      ...(minRating && { minRating }),
      ...(maxRating && { maxRating }),
      ...(tags && { tags }),
      ...(source && { source }),
    };

    // Build sort
    const sort = {
      field: sortBy,
      order: sortOrder,
    };

    // Build pagination
    const pagination = { page, limit };

    // Search products
    const result = await this.productService.searchProducts(q, filters, sort, pagination);

    return reply.paginated(
      result.products,
      page,
      limit,
      result.total,
      'Products retrieved successfully'
    );
  }

  async getProductById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as { id: string };

    const product = await this.productService.getProductById(id);

    if (!product) {
      throw new NotFoundError('Product');
    }

    return reply.success(product, 'Product retrieved successfully');
  }

  async getProductWithSentiment(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as { id: string };

    const product = await this.productService.getProductWithSentiment(id);

    if (!product) {
      throw new NotFoundError('Product');
    }

    return reply.success(product, 'Product with sentiment data retrieved successfully');
  }

  async getProductMentions(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as { id: string };
    const query = request.query as { source?: string; page?: number; limit?: number };

    // Validate pagination
    const paginationValidation = PaginationSchema.safeParse(query);
    if (!paginationValidation.success) {
      throw new ValidationError('Invalid pagination parameters', paginationValidation.error.errors);
    }

    const { page, limit } = paginationValidation.data;
    const { source } = query;

    const result = await this.productService.getProductMentions(id, { source }, { page, limit });

    return reply.paginated(
      result.mentions,
      page,
      limit,
      result.total,
      'Product mentions retrieved successfully'
    );
  }

  async createProduct(request: FastifyRequest, reply: FastifyReply) {
    // Validate request body
    const bodyValidation = ProductCreateSchema.safeParse(request.body);

    if (!bodyValidation.success) {
      throw new ValidationError('Invalid product data', bodyValidation.error.errors);
    }

    const productData = bodyValidation.data;

    const product = await this.productService.createProduct(productData as any);

    return reply.status(201).success(product, SUCCESS_MESSAGES.PRODUCT_CREATED);
  }

  async updateProduct(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as { id: string };

    // Validate request body (partial update)
    const bodyValidation = ProductCreateSchema.partial().safeParse(request.body);

    if (!bodyValidation.success) {
      throw new ValidationError('Invalid product data', bodyValidation.error.errors);
    }

    const updateData = bodyValidation.data;

    const product = await this.productService.updateProduct(id, updateData);

    return reply.success(product, SUCCESS_MESSAGES.PRODUCT_UPDATED);
  }

  async deleteProduct(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as { id: string };

    const deleted = await this.productService.deleteProduct(id);

    if (!deleted) {
      throw new NotFoundError('Product');
    }

    return reply.success(null, SUCCESS_MESSAGES.PRODUCT_DELETED);
  }

  async getCategories(request: FastifyRequest, reply: FastifyReply) {
    const categories = await this.productService.getCategories();

    return reply.success(categories, 'Product categories retrieved successfully');
  }

  async getBrands(request: FastifyRequest, reply: FastifyReply) {
    const brands = await this.productService.getBrands();

    return reply.success(brands, 'Product brands retrieved successfully');
  }

  async getRecentProducts(request: FastifyRequest, reply: FastifyReply) {
    const query = request.query as { limit?: number };
    const limit = Math.min(query.limit || 10, 50); // Max 50 products

    const products = await this.productService.getRecentProducts(limit);

    return reply.success(products, 'Recent products retrieved successfully');
  }

  async getPopularProducts(request: FastifyRequest, reply: FastifyReply) {
    const query = request.query as { limit?: number };
    const limit = Math.min(query.limit || 10, 50); // Max 50 products

    const products = await this.productService.getPopularProducts(limit);

    return reply.success(products, 'Popular products retrieved successfully');
  }
}
