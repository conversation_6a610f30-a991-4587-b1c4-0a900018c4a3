from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import os
from datetime import datetime
import time
import re
import numpy as np
from typing import Dict, List, Optional, Union

# AI/ML imports with fallback handling
try:
    from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
    from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
    from textblob import TextBlob
    import torch
    AI_MODELS_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("✅ AI models loaded successfully")
except ImportError as e:
    AI_MODELS_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning(f"⚠️ AI models not available, using fallback: {e}")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Initialize AI models
class SentimentAnalyzer:
    def __init__(self):
        self.models_loaded = False
        self.transformers_model = None
        self.vader_analyzer = None
        self.load_models()

    def load_models(self):
        """Load AI models with fallback handling"""
        try:
            if AI_MODELS_AVAILABLE:
                # Load RoBERTa model for sentiment analysis
                self.transformers_model = pipeline(
                    "sentiment-analysis",
                    model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                    return_all_scores=True
                )

                # Load VADER for social media text
                self.vader_analyzer = SentimentIntensityAnalyzer()

                self.models_loaded = True
                logger.info("✅ AI sentiment models loaded successfully")
            else:
                logger.warning("⚠️ AI models not available, using fallback")
        except Exception as e:
            logger.error(f"❌ Failed to load AI models: {e}")
            self.models_loaded = False

    def analyze_with_transformers(self, text: str) -> Dict:
        """Analyze sentiment using RoBERTa transformer model"""
        try:
            results = self.transformers_model(text)[0]

            # Convert to our standard format
            scores = {result['label'].lower(): result['score'] for result in results}

            # Map labels to our format
            positive_score = scores.get('positive', scores.get('pos', 0.0))
            negative_score = scores.get('negative', scores.get('neg', 0.0))
            neutral_score = scores.get('neutral', scores.get('neu', 0.0))

            # Calculate overall score (-1 to 1)
            overall_score = positive_score - negative_score

            # Calculate confidence as the highest score
            confidence_score = max(positive_score, negative_score, neutral_score)

            return {
                'overall_score': round(overall_score, 3),
                'positive_score': round(positive_score, 3),
                'negative_score': round(negative_score, 3),
                'neutral_score': round(neutral_score, 3),
                'confidence_score': round(confidence_score, 3),
                'model': 'roberta-sentiment'
            }
        except Exception as e:
            logger.error(f"Transformers analysis failed: {e}")
            return None

    def analyze_with_vader(self, text: str) -> Dict:
        """Analyze sentiment using VADER (good for social media text)"""
        try:
            scores = self.vader_analyzer.polarity_scores(text)

            return {
                'overall_score': round(scores['compound'], 3),
                'positive_score': round(scores['pos'], 3),
                'negative_score': round(scores['neg'], 3),
                'neutral_score': round(scores['neu'], 3),
                'confidence_score': round(abs(scores['compound']), 3),
                'model': 'vader-sentiment'
            }
        except Exception as e:
            logger.error(f"VADER analysis failed: {e}")
            return None

    def analyze_with_textblob(self, text: str) -> Dict:
        """Analyze sentiment using TextBlob (fallback method)"""
        try:
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity  # -1 to 1
            subjectivity = blob.sentiment.subjectivity  # 0 to 1

            # Convert polarity to positive/negative/neutral scores
            if polarity > 0:
                positive_score = polarity
                negative_score = 0.0
            elif polarity < 0:
                positive_score = 0.0
                negative_score = abs(polarity)
            else:
                positive_score = 0.0
                negative_score = 0.0

            neutral_score = 1.0 - positive_score - negative_score

            return {
                'overall_score': round(polarity, 3),
                'positive_score': round(positive_score, 3),
                'negative_score': round(negative_score, 3),
                'neutral_score': round(max(0.0, neutral_score), 3),
                'confidence_score': round(subjectivity, 3),
                'model': 'textblob-sentiment'
            }
        except Exception as e:
            logger.error(f"TextBlob analysis failed: {e}")
            return None

    def fallback_analysis(self, text: str) -> Dict:
        """Fallback sentiment analysis using simple word counting"""
        text_lower = text.lower()

        # Enhanced word lists
        positive_words = [
            'good', 'great', 'excellent', 'amazing', 'love', 'best', 'awesome',
            'fantastic', 'perfect', 'wonderful', 'outstanding', 'brilliant',
            'superb', 'magnificent', 'incredible', 'remarkable', 'exceptional',
            'impressive', 'beautiful', 'nice', 'happy', 'satisfied', 'pleased'
        ]
        negative_words = [
            'bad', 'terrible', 'awful', 'hate', 'worst', 'horrible',
            'disappointing', 'poor', 'useless', 'broken', 'disgusting',
            'pathetic', 'annoying', 'frustrating', 'angry', 'sad', 'upset',
            'disappointed', 'unsatisfied', 'displeased', 'regret', 'waste'
        ]

        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        total_words = len(text.split())

        if total_words == 0:
            return {
                'overall_score': 0.0,
                'positive_score': 0.0,
                'negative_score': 0.0,
                'neutral_score': 1.0,
                'confidence_score': 0.0,
                'model': 'fallback-wordcount'
            }

        # Calculate scores
        positive_ratio = positive_count / total_words
        negative_ratio = negative_count / total_words

        # Overall sentiment score (-1 to 1)
        overall_score = (positive_ratio - negative_ratio) * 2
        overall_score = max(-1.0, min(1.0, overall_score))

        # Normalize scores
        positive_score = min(1.0, positive_ratio * 3)
        negative_score = min(1.0, negative_ratio * 3)
        neutral_score = max(0.0, 1.0 - positive_score - negative_score)

        # Confidence based on presence of sentiment words
        confidence_score = min(1.0, (positive_count + negative_count) / max(1, total_words * 0.2))

        return {
            'overall_score': round(overall_score, 3),
            'positive_score': round(positive_score, 3),
            'negative_score': round(negative_score, 3),
            'neutral_score': round(neutral_score, 3),
            'confidence_score': round(confidence_score, 3),
            'model': 'fallback-wordcount'
        }

    def analyze(self, text: str, context: str = 'general') -> Dict:
        """
        Main sentiment analysis function with multiple model fallback
        """
        if not text or not text.strip():
            return {
                'overall_score': 0.0,
                'positive_score': 0.0,
                'negative_score': 0.0,
                'neutral_score': 1.0,
                'confidence_score': 0.0,
                'model': 'empty-text'
            }

        # Clean text
        text = text.strip()

        # Try models in order of preference
        if self.models_loaded:
            # For social media context, prefer VADER
            if context in ['social_media', 'comment', 'review']:
                result = self.analyze_with_vader(text)
                if result:
                    return result

            # Try RoBERTa transformer model
            result = self.analyze_with_transformers(text)
            if result:
                return result

            # Try VADER as fallback
            result = self.analyze_with_vader(text)
            if result:
                return result

        # Try TextBlob if available
        if AI_MODELS_AVAILABLE:
            result = self.analyze_with_textblob(text)
            if result:
                return result

        # Final fallback to word counting
        return self.fallback_analysis(text)

# Initialize global analyzer
sentiment_analyzer = SentimentAnalyzer()

def analyze_sentiment(text: str, context: str = 'general') -> Dict:
    """Wrapper function for backward compatibility"""
    return sentiment_analyzer.analyze(text, context)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'sentiment-analysis',
        'version': '1.0.0',
        'timestamp': datetime.utcnow().isoformat()
    })

@app.route('/analyze', methods=['POST'])
def analyze():
    """Analyze sentiment of a single text"""
    try:
        data = request.get_json()

        if not data or 'text' not in data:
            return jsonify({
                'error': 'Missing required field: text'
            }), 400

        text = data['text']

        if not text or not text.strip():
            return jsonify({
                'error': 'Text cannot be empty'
            }), 400

        if len(text) > 10000:
            return jsonify({
                'error': 'Text too long (max 10000 characters)'
            }), 400

        # Perform sentiment analysis
        start_time = time.time()
        result = analyze_sentiment(text)
        processing_time = time.time() - start_time

        response = {
            'success': True,
            'data': {
                'text': text[:100] + '...' if len(text) > 100 else text,
                'sentiment': result,
                'metadata': {
                    'text_length': len(text),
                    'word_count': len(text.split()),
                    'processing_time_ms': round(processing_time * 1000, 2),
                    'model': 'mock-sentiment-v1.0',
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
        }

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error in sentiment analysis: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@app.route('/analyze/batch', methods=['POST'])
def analyze_batch():
    """Analyze sentiment of multiple texts"""
    try:
        data = request.get_json()

        if not data or 'texts' not in data:
            return jsonify({
                'error': 'Missing required field: texts'
            }), 400

        texts = data['texts']

        if not isinstance(texts, list):
            return jsonify({
                'error': 'texts must be an array'
            }), 400

        if len(texts) == 0:
            return jsonify({
                'error': 'texts array cannot be empty'
            }), 400

        if len(texts) > 100:
            return jsonify({
                'error': 'Maximum 100 texts allowed per batch'
            }), 400

        # Validate each text
        for i, text in enumerate(texts):
            if not text or not text.strip():
                return jsonify({
                    'error': f'Text at index {i} cannot be empty'
                }), 400

            if len(text) > 10000:
                return jsonify({
                    'error': f'Text at index {i} too long (max 10000 characters)'
                }), 400

        # Perform batch sentiment analysis
        start_time = time.time()
        results = []

        for i, text in enumerate(texts):
            sentiment_result = analyze_sentiment(text)
            results.append({
                'index': i,
                'text': text[:100] + '...' if len(text) > 100 else text,
                'sentiment': sentiment_result,
                'metadata': {
                    'text_length': len(text),
                    'word_count': len(text.split())
                }
            })

        processing_time = time.time() - start_time

        response = {
            'success': True,
            'data': {
                'results': results,
                'summary': {
                    'total_texts': len(texts),
                    'average_sentiment': round(sum(r['sentiment']['overall_score'] for r in results) / len(results), 3),
                    'processing_time_ms': round(processing_time * 1000, 2),
                    'model': 'mock-sentiment-v1.0',
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
        }

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error in batch sentiment analysis: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@app.route('/models', methods=['GET'])
def get_models():
    """Get available sentiment analysis models"""
    return jsonify({
        'success': True,
        'data': {
            'available_models': [
                {
                    'id': 'mock-sentiment-v1.0',
                    'name': 'Mock Sentiment Analyzer',
                    'description': 'Basic mock sentiment analysis for development',
                    'languages': ['en'],
                    'accuracy': 0.65,
                    'speed': 'fast'
                }
            ],
            'default_model': 'mock-sentiment-v1.0'
        }
    })

@app.route('/stats', methods=['GET'])
def get_stats():
    """Get service statistics"""
    return jsonify({
        'success': True,
        'data': {
            'service': 'sentiment-analysis',
            'version': '1.0.0',
            'uptime': 'N/A',
            'requests_processed': 'N/A',
            'average_processing_time': 'N/A',
            'supported_languages': ['en'],
            'max_text_length': 10000,
            'max_batch_size': 100
        }
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'error': 'Method not allowed'
    }), 405

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'

    logger.info(f"Starting sentiment analysis service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
