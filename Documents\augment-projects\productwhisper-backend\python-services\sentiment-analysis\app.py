from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import os
from datetime import datetime
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Mock sentiment analysis function
def analyze_sentiment(text):
    """
    Mock sentiment analysis function.
    In production, this would use advanced NLP models like:
    - BERT, RoBERTa, or DistilBERT
    - VADER sentiment analyzer
    - TextBlob
    - spaCy with sentiment models
    - Hugging Face transformers
    """
    
    # Simple mock implementation
    text_lower = text.lower()
    
    # Count positive and negative words (very basic approach)
    positive_words = ['good', 'great', 'excellent', 'amazing', 'love', 'best', 'awesome', 'fantastic', 'perfect', 'wonderful']
    negative_words = ['bad', 'terrible', 'awful', 'hate', 'worst', 'horrible', 'disappointing', 'poor', 'useless', 'broken']
    
    positive_count = sum(1 for word in positive_words if word in text_lower)
    negative_count = sum(1 for word in negative_words if word in text_lower)
    
    total_words = len(text.split())
    
    if total_words == 0:
        return {
            'overall_score': 0.0,
            'positive_score': 0.0,
            'negative_score': 0.0,
            'neutral_score': 1.0,
            'confidence_score': 0.0
        }
    
    # Calculate scores
    positive_ratio = positive_count / total_words
    negative_ratio = negative_count / total_words
    
    # Overall sentiment score (-1 to 1)
    overall_score = (positive_ratio - negative_ratio)
    overall_score = max(-1.0, min(1.0, overall_score))
    
    # Normalize scores
    positive_score = min(1.0, positive_ratio * 2)
    negative_score = min(1.0, negative_ratio * 2)
    neutral_score = max(0.0, 1.0 - positive_score - negative_score)
    
    # Confidence based on presence of sentiment words
    confidence_score = min(1.0, (positive_count + negative_count) / max(1, total_words * 0.1))
    
    return {
        'overall_score': round(overall_score, 3),
        'positive_score': round(positive_score, 3),
        'negative_score': round(negative_score, 3),
        'neutral_score': round(neutral_score, 3),
        'confidence_score': round(confidence_score, 3)
    }

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'sentiment-analysis',
        'version': '1.0.0',
        'timestamp': datetime.utcnow().isoformat()
    })

@app.route('/analyze', methods=['POST'])
def analyze():
    """Analyze sentiment of a single text"""
    try:
        data = request.get_json()
        
        if not data or 'text' not in data:
            return jsonify({
                'error': 'Missing required field: text'
            }), 400
        
        text = data['text']
        
        if not text or not text.strip():
            return jsonify({
                'error': 'Text cannot be empty'
            }), 400
        
        if len(text) > 10000:
            return jsonify({
                'error': 'Text too long (max 10000 characters)'
            }), 400
        
        # Perform sentiment analysis
        start_time = time.time()
        result = analyze_sentiment(text)
        processing_time = time.time() - start_time
        
        response = {
            'success': True,
            'data': {
                'text': text[:100] + '...' if len(text) > 100 else text,
                'sentiment': result,
                'metadata': {
                    'text_length': len(text),
                    'word_count': len(text.split()),
                    'processing_time_ms': round(processing_time * 1000, 2),
                    'model': 'mock-sentiment-v1.0',
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error in sentiment analysis: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@app.route('/analyze/batch', methods=['POST'])
def analyze_batch():
    """Analyze sentiment of multiple texts"""
    try:
        data = request.get_json()
        
        if not data or 'texts' not in data:
            return jsonify({
                'error': 'Missing required field: texts'
            }), 400
        
        texts = data['texts']
        
        if not isinstance(texts, list):
            return jsonify({
                'error': 'texts must be an array'
            }), 400
        
        if len(texts) == 0:
            return jsonify({
                'error': 'texts array cannot be empty'
            }), 400
        
        if len(texts) > 100:
            return jsonify({
                'error': 'Maximum 100 texts allowed per batch'
            }), 400
        
        # Validate each text
        for i, text in enumerate(texts):
            if not text or not text.strip():
                return jsonify({
                    'error': f'Text at index {i} cannot be empty'
                }), 400
            
            if len(text) > 10000:
                return jsonify({
                    'error': f'Text at index {i} too long (max 10000 characters)'
                }), 400
        
        # Perform batch sentiment analysis
        start_time = time.time()
        results = []
        
        for i, text in enumerate(texts):
            sentiment_result = analyze_sentiment(text)
            results.append({
                'index': i,
                'text': text[:100] + '...' if len(text) > 100 else text,
                'sentiment': sentiment_result,
                'metadata': {
                    'text_length': len(text),
                    'word_count': len(text.split())
                }
            })
        
        processing_time = time.time() - start_time
        
        response = {
            'success': True,
            'data': {
                'results': results,
                'summary': {
                    'total_texts': len(texts),
                    'average_sentiment': round(sum(r['sentiment']['overall_score'] for r in results) / len(results), 3),
                    'processing_time_ms': round(processing_time * 1000, 2),
                    'model': 'mock-sentiment-v1.0',
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error in batch sentiment analysis: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@app.route('/models', methods=['GET'])
def get_models():
    """Get available sentiment analysis models"""
    return jsonify({
        'success': True,
        'data': {
            'available_models': [
                {
                    'id': 'mock-sentiment-v1.0',
                    'name': 'Mock Sentiment Analyzer',
                    'description': 'Basic mock sentiment analysis for development',
                    'languages': ['en'],
                    'accuracy': 0.65,
                    'speed': 'fast'
                }
            ],
            'default_model': 'mock-sentiment-v1.0'
        }
    })

@app.route('/stats', methods=['GET'])
def get_stats():
    """Get service statistics"""
    return jsonify({
        'success': True,
        'data': {
            'service': 'sentiment-analysis',
            'version': '1.0.0',
            'uptime': 'N/A',
            'requests_processed': 'N/A',
            'average_processing_time': 'N/A',
            'supported_languages': ['en'],
            'max_text_length': 10000,
            'max_batch_size': 100
        }
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'error': 'Method not allowed'
    }), 405

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    logger.info(f"Starting sentiment analysis service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
