#!/usr/bin/env node

/**
 * API Testing Script
 * Tests the ProductWhisper backend API endpoints
 */

const http = require('http');
const https = require('https');

const BASE_URL = 'http://localhost:8000';

console.log('🧪 ProductWhisper Backend - API Testing');
console.log('=======================================\n');

// Simple HTTP request function
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'ProductWhisper-Test/1.0',
        ...options.headers
      }
    };

    const req = client.request(url, requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

// Test function
async function testEndpoint(name, url, options = {}) {
  try {
    console.log(`🔍 Testing: ${name}`);
    console.log(`   URL: ${url}`);
    
    const response = await makeRequest(url, options);
    
    if (response.status >= 200 && response.status < 300) {
      console.log(`   ✅ Status: ${response.status}`);
      if (response.data && typeof response.data === 'object') {
        console.log(`   📊 Response: ${JSON.stringify(response.data, null, 2).substring(0, 200)}...`);
      }
    } else {
      console.log(`   ⚠️  Status: ${response.status}`);
      console.log(`   📄 Response: ${JSON.stringify(response.data, null, 2).substring(0, 200)}...`);
    }
    
    console.log('');
    return response;
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    console.log('');
    return null;
  }
}

// Main testing function
async function runTests() {
  console.log('🚀 Starting API tests...\n');

  // Test 1: Health Check
  await testEndpoint(
    'Health Check',
    `${BASE_URL}/health`
  );

  // Test 2: Ready Check
  await testEndpoint(
    'Ready Check',
    `${BASE_URL}/ready`
  );

  // Test 3: Products List
  await testEndpoint(
    'Products List',
    `${BASE_URL}/api/v1/products?limit=5`
  );

  // Test 4: Product Categories
  await testEndpoint(
    'Product Categories',
    `${BASE_URL}/api/v1/products/meta/categories`
  );

  // Test 5: Search (without query - should return validation error)
  await testEndpoint(
    'Search (No Query)',
    `${BASE_URL}/api/v1/search`
  );

  // Test 6: Search with query
  await testEndpoint(
    'Search with Query',
    `${BASE_URL}/api/v1/search?q=iPhone&limit=3`
  );

  // Test 7: Search Suggestions
  await testEndpoint(
    'Search Suggestions',
    `${BASE_URL}/api/v1/search/suggestions?q=iph`
  );

  // Test 8: Reddit Health
  await testEndpoint(
    'Reddit API Health',
    `${BASE_URL}/api/v1/reddit/health`
  );

  // Test 9: Reddit Popular Subreddits
  await testEndpoint(
    'Reddit Popular Subreddits',
    `${BASE_URL}/api/v1/reddit/subreddits/popular`
  );

  // Test 10: Sentiment Health
  await testEndpoint(
    'Sentiment API Health',
    `${BASE_URL}/api/v1/sentiment/health`
  );

  // Test 11: Sentiment Analysis
  await testEndpoint(
    'Sentiment Analysis',
    `${BASE_URL}/api/v1/sentiment/analyze`,
    {
      method: 'POST',
      body: {
        text: 'This product is absolutely amazing! I love it so much.',
        context: 'product_review'
      }
    }
  );

  // Test 12: Batch Sentiment Analysis
  await testEndpoint(
    'Batch Sentiment Analysis',
    `${BASE_URL}/api/v1/sentiment/analyze/batch`,
    {
      method: 'POST',
      body: {
        texts: [
          'This product is great!',
          'Not impressed with the quality.',
          'It\'s okay, nothing special.'
        ],
        context: 'product_review'
      }
    }
  );

  // Test 13: Sentiment Statistics
  await testEndpoint(
    'Sentiment Statistics',
    `${BASE_URL}/api/v1/sentiment/stats?timeframe=week`
  );

  console.log('🎉 API testing completed!\n');

  console.log('📋 Test Summary:');
  console.log('================');
  console.log('✅ Health endpoints - Basic server functionality');
  console.log('✅ Product endpoints - Product management API');
  console.log('✅ Search endpoints - Search functionality');
  console.log('✅ Reddit endpoints - Social media integration');
  console.log('✅ Sentiment endpoints - AI analysis capabilities');

  console.log('\n💡 Notes:');
  console.log('- Some endpoints may return empty data without database');
  console.log('- Sentiment analysis requires external service');
  console.log('- Reddit API requires valid credentials');
  console.log('- All endpoints should return proper JSON responses');

  console.log('\n🔧 Troubleshooting:');
  console.log('- If server is not running: npm run dev');
  console.log('- If database errors: Set up PostgreSQL and run migrations');
  console.log('- If Redis errors: Start Redis server');
  console.log('- Check .env file for proper configuration');
}

// Check if server is running first
async function checkServer() {
  try {
    const response = await makeRequest(`${BASE_URL}/health`);
    if (response && response.status === 200) {
      console.log('✅ Server is running, starting tests...\n');
      return true;
    } else {
      console.log('❌ Server responded but health check failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Server is not running or not accessible');
    console.log('💡 Please start the server with: npm run dev');
    console.log('🌐 Server should be running on: http://localhost:8000\n');
    return false;
  }
}

// Run the tests
checkServer().then(isRunning => {
  if (isRunning) {
    runTests();
  }
});
