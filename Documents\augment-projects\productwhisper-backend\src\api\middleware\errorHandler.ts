import { FastifyError, FastifyRequest, FastifyReply } from 'fastify';
import { ZodError } from 'zod';
import { AppError, getErrorResponse, getErrorLogLevel, isOperationalError } from '@/shared/errors';
import { HTTP_STATUS } from '@/shared/constants';

export async function errorHandler(
  error: FastifyError | AppError | Error,
  request: FastifyRequest,
  reply: FastifyReply
) {
  const requestId = request.id;
  const logLevel = getErrorLogLevel(error as AppError);

  // Log the error
  (request.log as any)[logLevel]({
    error: {
      message: error.message,
      stack: error.stack,
      code: (error as any).code,
      statusCode: (error as any).statusCode,
    },
    requestId,
    url: request.url,
    method: request.method,
    ip: request.ip,
    userAgent: request.headers['user-agent'],
  }, 'Request error occurred');

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    const validationErrors = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code,
    }));

    return reply.status(HTTP_STATUS.BAD_REQUEST).send({
      success: false,
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: {
        errors: validationErrors,
      },
      timestamp: new Date().toISOString(),
      requestId,
    });
  }

  // Handle Fastify validation errors
  if ((error as any).validation) {
    return reply.status(HTTP_STATUS.BAD_REQUEST).send({
      success: false,
      error: 'Request validation failed',
      code: 'VALIDATION_ERROR',
      details: {
        validation: (error as any).validation,
        validationContext: (error as any).validationContext,
      },
      timestamp: new Date().toISOString(),
      requestId,
    });
  }

  // Handle Prisma errors
  if (error.message.includes('Prisma')) {
    let statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR;
    let message = 'Database error occurred';
    let code = 'DATABASE_ERROR';

    // Handle specific Prisma errors
    if (error.message.includes('Unique constraint')) {
      statusCode = HTTP_STATUS.CONFLICT;
      message = 'Resource already exists';
      code = 'DUPLICATE_RESOURCE';
    } else if (error.message.includes('Foreign key constraint')) {
      statusCode = HTTP_STATUS.BAD_REQUEST;
      message = 'Invalid reference to related resource';
      code = 'INVALID_REFERENCE';
    } else if (error.message.includes('Record to update not found')) {
      statusCode = HTTP_STATUS.NOT_FOUND;
      message = 'Resource not found';
      code = 'NOT_FOUND';
    } else if (error.message.includes('Record to delete does not exist')) {
      statusCode = HTTP_STATUS.NOT_FOUND;
      message = 'Resource not found';
      code = 'NOT_FOUND';
    }

    return reply.status(statusCode).send({
      success: false,
      error: message,
      code,
      timestamp: new Date().toISOString(),
      requestId,
    });
  }

  // Handle Redis/Cache errors
  if (error.message.includes('Redis') || error.message.includes('Cache')) {
    // Cache errors shouldn't break the request, log and continue
    request.log.warn({
      error: error.message,
      requestId,
    }, 'Cache error occurred, continuing without cache');

    // Don't return error for cache failures, let the request continue
    // This should be handled at the service level
  }

  // Handle rate limiting errors
  if ((error as any).statusCode === HTTP_STATUS.TOO_MANY_REQUESTS) {
    return reply.status(HTTP_STATUS.TOO_MANY_REQUESTS).send({
      success: false,
      error: 'Rate limit exceeded',
      code: 'RATE_LIMIT_EXCEEDED',
      details: {
        retryAfter: reply.getHeader('retry-after'),
        limit: reply.getHeader('x-ratelimit-limit'),
        remaining: reply.getHeader('x-ratelimit-remaining'),
        reset: reply.getHeader('x-ratelimit-reset'),
      },
      timestamp: new Date().toISOString(),
      requestId,
    });
  }

  // Handle custom application errors
  if (error instanceof AppError) {
    const response = getErrorResponse(error);
    (response as any).requestId = requestId;
    return reply.status(error.statusCode).send(response);
  }

  // Handle Fastify errors
  if ((error as FastifyError).statusCode) {
    const statusCode = (error as FastifyError).statusCode!;
    let message = error.message;
    let code = 'FASTIFY_ERROR';

    // Handle specific Fastify errors
    switch (statusCode) {
      case HTTP_STATUS.BAD_REQUEST:
        code = 'BAD_REQUEST';
        break;
      case HTTP_STATUS.UNAUTHORIZED:
        code = 'UNAUTHORIZED';
        break;
      case HTTP_STATUS.FORBIDDEN:
        code = 'FORBIDDEN';
        break;
      case HTTP_STATUS.NOT_FOUND:
        message = 'Endpoint not found';
        code = 'ENDPOINT_NOT_FOUND';
        break;
      case HTTP_STATUS.METHOD_NOT_ALLOWED:
        code = 'METHOD_NOT_ALLOWED';
        break;
      case HTTP_STATUS.UNPROCESSABLE_ENTITY:
        code = 'UNPROCESSABLE_ENTITY';
        break;
      case HTTP_STATUS.INTERNAL_SERVER_ERROR:
        message = 'Internal server error';
        code = 'INTERNAL_ERROR';
        break;
      default:
        code = 'HTTP_ERROR';
    }

    return reply.status(statusCode).send({
      success: false,
      error: message,
      code,
      timestamp: new Date().toISOString(),
      requestId,
    });
  }

  // Handle unknown errors
  const isOperational = isOperationalError(error);

  if (!isOperational) {
    // Log non-operational errors with full stack trace
    request.log.error({
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name,
      },
      requestId,
      url: request.url,
      method: request.method,
    }, 'Non-operational error occurred');
  }

  // Return generic error response for unknown errors
  return reply.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).send({
    success: false,
    error: isOperational ? error.message : 'Internal server error',
    code: 'INTERNAL_ERROR',
    timestamp: new Date().toISOString(),
    requestId,
  });
}
