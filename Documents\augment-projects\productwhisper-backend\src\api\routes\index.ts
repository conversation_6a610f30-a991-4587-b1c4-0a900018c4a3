import { FastifyInstance } from 'fastify';
import { serverConfig } from '@/config';

// Import route modules
import { productRoutes } from './products';
import { searchRoutes } from './search';
import { sentimentRoutes } from './sentiment';
import { trendsRoutes } from './trends';
import { redditRoutes } from './reddit';
import { chatRoutes } from './chat';
import { comparisonRoutes } from './comparison';
import { analyticsRoutes } from './analytics';

export async function setupRoutes(fastify: FastifyInstance) {
  const apiPrefix = serverConfig.apiPrefix;

  // Register API routes with prefix
  await fastify.register(async function (fastify) {
    // Products routes
    await fastify.register(productRoutes, { prefix: '/products' });
    
    // Search routes
    await fastify.register(searchRoutes, { prefix: '/search' });
    
    // Sentiment analysis routes
    await fastify.register(sentimentRoutes, { prefix: '/sentiment' });
    
    // Trends routes
    await fastify.register(trendsRoutes, { prefix: '/trends' });
    
    // Reddit integration routes
    await fastify.register(redditRoutes, { prefix: '/reddit' });
    
    // Chat/FAQ routes
    await fastify.register(chatRoutes, { prefix: '/chat' });
    
    // Product comparison routes
    await fastify.register(comparisonRoutes, { prefix: '/comparison' });
    
    // Analytics routes
    await fastify.register(analyticsRoutes, { prefix: '/analytics' });
    
  }, { prefix: apiPrefix });

  // Root API endpoint
  fastify.get(apiPrefix, async (request, reply) => {
    return reply.send({
      success: true,
      message: 'ProductWhisper API',
      version: '1.0.0',
      documentation: '/docs',
      endpoints: {
        health: '/health',
        products: `${apiPrefix}/products`,
        search: `${apiPrefix}/search`,
        sentiment: `${apiPrefix}/sentiment`,
        trends: `${apiPrefix}/trends`,
        reddit: `${apiPrefix}/reddit`,
        chat: `${apiPrefix}/chat`,
        comparison: `${apiPrefix}/comparison`,
        analytics: `${apiPrefix}/analytics`,
      },
      timestamp: new Date().toISOString(),
    });
  });

  // Catch-all route for undefined endpoints
  fastify.setNotFoundHandler(async (request, reply) => {
    return reply.status(404).send({
      success: false,
      error: 'Endpoint not found',
      code: 'ENDPOINT_NOT_FOUND',
      message: `The endpoint ${request.method} ${request.url} does not exist`,
      availableEndpoints: {
        api: apiPrefix,
        health: '/health',
        docs: '/docs',
      },
      timestamp: new Date().toISOString(),
      requestId: request.id,
    });
  });

  fastify.log.info('✅ All routes registered successfully');
}
