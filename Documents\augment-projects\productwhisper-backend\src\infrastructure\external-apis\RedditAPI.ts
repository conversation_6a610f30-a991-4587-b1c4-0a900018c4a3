import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { redditConfig } from '@/config';
import { CacheService } from '@/infrastructure/cache/CacheService';
import { createCacheKey } from '@/shared/utils';
import { CACHE_TTL } from '@/shared/constants';

export interface RedditPost {
  id: string;
  title: string;
  selftext: string;
  author: string;
  subreddit: string;
  score: number;
  num_comments: number;
  created_utc: number;
  url: string;
  permalink: string;
  upvote_ratio: number;
  over_18: boolean;
  is_self: boolean;
}

export interface RedditComment {
  id: string;
  body: string;
  author: string;
  score: number;
  created_utc: number;
  permalink: string;
  parent_id: string;
  link_id: string;
}

export interface RedditSearchResult {
  posts: RedditPost[];
  comments: RedditComment[];
  after?: string;
  before?: string;
  total?: number;
}

export interface RedditSearchOptions {
  subreddit?: string;
  sort?: 'relevance' | 'hot' | 'top' | 'new' | 'comments';
  time?: 'all' | 'year' | 'month' | 'week' | 'day' | 'hour';
  limit?: number;
  after?: string;
  before?: string;
}

export class RedditAPI {
  private client: AxiosInstance;
  private cacheService: CacheService;
  private accessToken?: string;
  private tokenExpiry?: Date;
  private rateLimitRemaining: number = 60;
  private rateLimitReset: Date = new Date();

  constructor() {
    this.client = axios.create({
      baseURL: 'https://oauth.reddit.com',
      headers: {
        'User-Agent': redditConfig.userAgent,
      },
      timeout: 30000,
    });

    this.cacheService = new CacheService();
    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor for authentication
    this.client.interceptors.request.use(async (config) => {
      await this.ensureAuthenticated();
      if (this.accessToken) {
        config.headers.Authorization = `Bearer ${this.accessToken}`;
      }
      return config;
    });

    // Response interceptor for rate limiting
    this.client.interceptors.response.use(
      (response) => {
        this.updateRateLimit(response);
        return response;
      },
      (error) => {
        if (error.response) {
          this.updateRateLimit(error.response);
          
          if (error.response.status === 429) {
            throw new Error('Reddit API rate limit exceeded');
          }
          
          if (error.response.status === 401) {
            this.accessToken = undefined;
            throw new Error('Reddit API authentication failed');
          }
        }
        throw error;
      }
    );
  }

  private updateRateLimit(response: AxiosResponse) {
    const remaining = response.headers['x-ratelimit-remaining'];
    const reset = response.headers['x-ratelimit-reset'];
    
    if (remaining) {
      this.rateLimitRemaining = parseInt(remaining);
    }
    
    if (reset) {
      this.rateLimitReset = new Date(parseInt(reset) * 1000);
    }
  }

  private async ensureAuthenticated(): Promise<void> {
    if (this.accessToken && this.tokenExpiry && this.tokenExpiry > new Date()) {
      return;
    }

    await this.authenticate();
  }

  private async authenticate(): Promise<void> {
    try {
      const authClient = axios.create({
        baseURL: 'https://www.reddit.com',
        auth: {
          username: redditConfig.clientId,
          password: redditConfig.clientSecret,
        },
        headers: {
          'User-Agent': redditConfig.userAgent,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      const response = await authClient.post('/api/v1/access_token', 
        'grant_type=client_credentials'
      );

      this.accessToken = response.data.access_token;
      this.tokenExpiry = new Date(Date.now() + (response.data.expires_in * 1000) - 60000); // 1 minute buffer

      console.log('✅ Reddit API authenticated successfully');
    } catch (error) {
      console.error('❌ Reddit API authentication failed:', error);
      throw new Error('Failed to authenticate with Reddit API');
    }
  }

  async searchPosts(query: string, options: RedditSearchOptions = {}): Promise<RedditSearchResult> {
    await this.checkRateLimit();

    const cacheKey = createCacheKey('reddit', 'search', 'posts', query, JSON.stringify(options));
    const cached = await this.cacheService.get<RedditSearchResult>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const params = new URLSearchParams({
        q: query,
        type: 'link',
        sort: options.sort || 'relevance',
        t: options.time || 'all',
        limit: (options.limit || 25).toString(),
        ...(options.after && { after: options.after }),
        ...(options.before && { before: options.before }),
        ...(options.subreddit && { restrict_sr: 'true' }),
      });

      const endpoint = options.subreddit 
        ? `/r/${options.subreddit}/search`
        : '/search';

      const response = await this.client.get(`${endpoint}?${params}`);
      const posts = this.parseRedditPosts(response.data.data.children);

      const result: RedditSearchResult = {
        posts,
        comments: [],
        after: response.data.data.after,
        before: response.data.data.before,
      };

      await this.cacheService.set(cacheKey, result, { ttl: CACHE_TTL.SHORT });
      return result;
    } catch (error) {
      console.error('Reddit search posts error:', error);
      throw new Error('Failed to search Reddit posts');
    }
  }

  async searchComments(query: string, options: RedditSearchOptions = {}): Promise<RedditSearchResult> {
    await this.checkRateLimit();

    const cacheKey = createCacheKey('reddit', 'search', 'comments', query, JSON.stringify(options));
    const cached = await this.cacheService.get<RedditSearchResult>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const params = new URLSearchParams({
        q: query,
        type: 'comment',
        sort: options.sort || 'relevance',
        t: options.time || 'all',
        limit: (options.limit || 25).toString(),
        ...(options.after && { after: options.after }),
        ...(options.before && { before: options.before }),
        ...(options.subreddit && { restrict_sr: 'true' }),
      });

      const endpoint = options.subreddit 
        ? `/r/${options.subreddit}/search`
        : '/search';

      const response = await this.client.get(`${endpoint}?${params}`);
      const comments = this.parseRedditComments(response.data.data.children);

      const result: RedditSearchResult = {
        posts: [],
        comments,
        after: response.data.data.after,
        before: response.data.data.before,
      };

      await this.cacheService.set(cacheKey, result, { ttl: CACHE_TTL.SHORT });
      return result;
    } catch (error) {
      console.error('Reddit search comments error:', error);
      throw new Error('Failed to search Reddit comments');
    }
  }

  async getSubredditPosts(subreddit: string, sort: 'hot' | 'new' | 'top' = 'hot', limit: number = 25): Promise<RedditPost[]> {
    await this.checkRateLimit();

    const cacheKey = createCacheKey('reddit', 'subreddit', subreddit, sort, limit.toString());
    const cached = await this.cacheService.get<RedditPost[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await this.client.get(`/r/${subreddit}/${sort}`, {
        params: { limit }
      });

      const posts = this.parseRedditPosts(response.data.data.children);
      await this.cacheService.set(cacheKey, posts, { ttl: CACHE_TTL.SHORT });
      
      return posts;
    } catch (error) {
      console.error('Reddit get subreddit posts error:', error);
      throw new Error(`Failed to get posts from r/${subreddit}`);
    }
  }

  async getPostComments(postId: string, limit: number = 100): Promise<RedditComment[]> {
    await this.checkRateLimit();

    const cacheKey = createCacheKey('reddit', 'comments', postId, limit.toString());
    const cached = await this.cacheService.get<RedditComment[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await this.client.get(`/comments/${postId}`, {
        params: { limit }
      });

      // Reddit returns an array where [0] is the post and [1] is comments
      const commentsData = response.data[1]?.data?.children || [];
      const comments = this.parseRedditComments(commentsData);
      
      await this.cacheService.set(cacheKey, comments, { ttl: CACHE_TTL.MEDIUM });
      
      return comments;
    } catch (error) {
      console.error('Reddit get post comments error:', error);
      throw new Error(`Failed to get comments for post ${postId}`);
    }
  }

  private parseRedditPosts(children: any[]): RedditPost[] {
    return children
      .filter(child => child.kind === 't3' && child.data)
      .map(child => {
        const data = child.data;
        return {
          id: data.id,
          title: data.title,
          selftext: data.selftext || '',
          author: data.author,
          subreddit: data.subreddit,
          score: data.score,
          num_comments: data.num_comments,
          created_utc: data.created_utc,
          url: data.url,
          permalink: `https://reddit.com${data.permalink}`,
          upvote_ratio: data.upvote_ratio,
          over_18: data.over_18,
          is_self: data.is_self,
        };
      });
  }

  private parseRedditComments(children: any[]): RedditComment[] {
    const comments: RedditComment[] = [];
    
    const parseComment = (child: any) => {
      if (child.kind === 't1' && child.data && child.data.body) {
        const data = child.data;
        comments.push({
          id: data.id,
          body: data.body,
          author: data.author,
          score: data.score,
          created_utc: data.created_utc,
          permalink: `https://reddit.com${data.permalink}`,
          parent_id: data.parent_id,
          link_id: data.link_id,
        });

        // Parse nested replies
        if (data.replies && data.replies.data && data.replies.data.children) {
          data.replies.data.children.forEach(parseComment);
        }
      }
    };

    children.forEach(parseComment);
    return comments;
  }

  private async checkRateLimit(): Promise<void> {
    if (this.rateLimitRemaining <= 1) {
      const waitTime = Math.max(0, this.rateLimitReset.getTime() - Date.now());
      if (waitTime > 0) {
        console.log(`⏳ Reddit API rate limit reached, waiting ${waitTime}ms`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  getRateLimitStatus(): { remaining: number; resetTime: Date } {
    return {
      remaining: this.rateLimitRemaining,
      resetTime: this.rateLimitReset,
    };
  }

  async healthCheck(): Promise<boolean> {
    try {
      await this.ensureAuthenticated();
      return !!this.accessToken;
    } catch (error) {
      return false;
    }
  }
}
