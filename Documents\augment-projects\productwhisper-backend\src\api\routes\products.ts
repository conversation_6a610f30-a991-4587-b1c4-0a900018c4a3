import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { ProductController } from '@/api/controllers/ProductController';
import { ProductSearchSchema, PaginationSchema, ProductCreateSchema } from '@/shared/types';
import { asyncHand<PERSON> } from '@/shared/errors';

const productController = new ProductController();

export async function productRoutes(fastify: FastifyInstance) {
  // Get all products with search and filtering
  fastify.get('/', {
    schema: {
      tags: ['Products'],
      summary: 'Search and filter products',
      description: 'Get a list of products with optional search and filtering parameters',
      querystring: {
        type: 'object',
        properties: {
          q: { type: 'string', description: 'Search query' },
          category: { type: 'string', description: 'Filter by category' },
          brand: { type: 'string', description: 'Filter by brand' },
          minPrice: { type: 'number', description: 'Minimum price filter' },
          maxPrice: { type: 'number', description: 'Maximum price filter' },
          minRating: { type: 'number', description: 'Minimum rating filter' },
          maxRating: { type: 'number', description: 'Maximum rating filter' },
          tags: { type: 'array', items: { type: 'string' }, description: 'Filter by tags' },
          source: { type: 'array', items: { type: 'string' }, description: 'Filter by source platforms' },
          sortBy: { type: 'string', enum: ['name', 'createdAt', 'updatedAt', 'sentiment', 'popularity'], default: 'createdAt' },
          sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' },
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: { $ref: '#/definitions/Product' },
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'integer' },
                limit: { type: 'integer' },
                total: { type: 'integer' },
                totalPages: { type: 'integer' },
                hasNext: { type: 'boolean' },
                hasPrev: { type: 'boolean' },
              },
            },
            timestamp: { type: 'string' },
            requestId: { type: 'string' },
          },
        },
      },
    },
  }, asyncHandler(productController.searchProducts.bind(productController)));

  // Get product by ID
  fastify.get('/:id', {
    schema: {
      tags: ['Products'],
      summary: 'Get product by ID',
      description: 'Get detailed information about a specific product',
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', format: 'uuid', description: 'Product ID' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { $ref: '#/definitions/Product' },
            timestamp: { type: 'string' },
            requestId: { type: 'string' },
          },
        },
        404: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            error: { type: 'string' },
            code: { type: 'string' },
            timestamp: { type: 'string' },
            requestId: { type: 'string' },
          },
        },
      },
    },
  }, asyncHandler(productController.getProductById.bind(productController)));

  // Get product with sentiment analysis
  fastify.get('/:id/sentiment', {
    schema: {
      tags: ['Products'],
      summary: 'Get product with sentiment analysis',
      description: 'Get product information including sentiment scores and mentions',
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', format: 'uuid', description: 'Product ID' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              allOf: [
                { $ref: '#/definitions/Product' },
                {
                  type: 'object',
                  properties: {
                    sentimentScores: {
                      type: 'array',
                      items: { $ref: '#/definitions/SentimentScore' },
                    },
                    averageSentiment: { type: 'number' },
                    totalMentions: { type: 'integer' },
                    trendScore: { type: 'number' },
                  },
                },
              ],
            },
            timestamp: { type: 'string' },
            requestId: { type: 'string' },
          },
        },
      },
    },
  }, asyncHandler(productController.getProductWithSentiment.bind(productController)));

  // Get product mentions
  fastify.get('/:id/mentions', {
    schema: {
      tags: ['Products'],
      summary: 'Get product mentions',
      description: 'Get mentions and reviews for a specific product',
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', format: 'uuid', description: 'Product ID' },
        },
      },
      querystring: {
        type: 'object',
        properties: {
          source: { type: 'string', description: 'Filter by source platform' },
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
        },
      },
    },
  }, asyncHandler(productController.getProductMentions.bind(productController)));

  // Create new product
  fastify.post('/', {
    schema: {
      tags: ['Products'],
      summary: 'Create new product',
      description: 'Create a new product entry',
      body: {
        type: 'object',
        required: ['name'],
        properties: {
          name: { type: 'string', minLength: 1, maxLength: 255 },
          description: { type: 'string', maxLength: 5000 },
          category: { type: 'string', maxLength: 100 },
          brand: { type: 'string', maxLength: 100 },
          imageUrls: { type: 'array', items: { type: 'string', format: 'uri' }, maxItems: 10 },
          externalIds: { type: 'object' },
          metadata: { type: 'object' },
        },
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { $ref: '#/definitions/Product' },
            message: { type: 'string' },
            timestamp: { type: 'string' },
            requestId: { type: 'string' },
          },
        },
      },
    },
  }, asyncHandler(productController.createProduct.bind(productController)));

  // Update product
  fastify.put('/:id', {
    schema: {
      tags: ['Products'],
      summary: 'Update product',
      description: 'Update an existing product',
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', format: 'uuid', description: 'Product ID' },
        },
      },
      body: {
        type: 'object',
        properties: {
          name: { type: 'string', minLength: 1, maxLength: 255 },
          description: { type: 'string', maxLength: 5000 },
          category: { type: 'string', maxLength: 100 },
          brand: { type: 'string', maxLength: 100 },
          imageUrls: { type: 'array', items: { type: 'string', format: 'uri' }, maxItems: 10 },
          externalIds: { type: 'object' },
          metadata: { type: 'object' },
        },
      },
    },
  }, asyncHandler(productController.updateProduct.bind(productController)));

  // Delete product
  fastify.delete('/:id', {
    schema: {
      tags: ['Products'],
      summary: 'Delete product',
      description: 'Delete a product and all associated data',
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', format: 'uuid', description: 'Product ID' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            timestamp: { type: 'string' },
            requestId: { type: 'string' },
          },
        },
      },
    },
  }, asyncHandler(productController.deleteProduct.bind(productController)));

  // Get product categories
  fastify.get('/meta/categories', {
    schema: {
      tags: ['Products'],
      summary: 'Get product categories',
      description: 'Get list of available product categories with counts',
    },
  }, asyncHandler(productController.getCategories.bind(productController)));

  // Get product brands
  fastify.get('/meta/brands', {
    schema: {
      tags: ['Products'],
      summary: 'Get product brands',
      description: 'Get list of available product brands with counts',
    },
  }, asyncHandler(productController.getBrands.bind(productController)));

  // Get recently added products
  fastify.get('/recent', {
    schema: {
      tags: ['Products'],
      summary: 'Get recently added products',
      description: 'Get list of recently added products',
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', minimum: 1, maximum: 50, default: 10 },
        },
      },
    },
  }, asyncHandler(productController.getRecentProducts.bind(productController)));

  // Get popular products
  fastify.get('/popular', {
    schema: {
      tags: ['Products'],
      summary: 'Get popular products',
      description: 'Get list of popular products based on mentions and sentiment',
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', minimum: 1, maximum: 50, default: 10 },
        },
      },
    },
  }, asyncHandler(productController.getPopularProducts.bind(productController)));
}
