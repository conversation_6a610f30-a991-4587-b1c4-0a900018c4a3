{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "declarationMap": false, "sourceMap": true, "removeComments": true, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnusedLabels": true, "allowUnreachableCode": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/api/*": ["api/*"], "@/core/*": ["core/*"], "@/infrastructure/*": ["infrastructure/*"], "@/shared/*": ["shared/*"], "@/config/*": ["config/*"], "@/tests/*": ["../tests/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "python-services"], "ts-node": {"require": ["tsconfig-paths/register"]}}