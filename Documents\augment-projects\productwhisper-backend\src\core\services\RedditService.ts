import { RedditAPI, RedditPost, RedditComment, RedditSearchOptions } from '@/infrastructure/external-apis/RedditAPI';
import { ProductRepository, PrismaProductRepository } from '@/core/repositories/ProductRepository';
import { ProductEntity } from '@/core/entities/Product';
import { CacheService } from '@/infrastructure/cache/CacheService';
import { prisma } from '@/infrastructure/database/prisma';
import { CACHE_KEYS, CACHE_TTL } from '@/shared/constants';
import { createCacheKey } from '@/shared/utils';
import { PaginationParams } from '@/shared/types';

export interface ProductMention {
  id: string;
  productId: string;
  source: 'REDDIT';
  sourceId: string;
  url?: string;
  content: string;
  sentimentScore?: number;
  engagementMetrics: Record<string, any>;
  authorInfo: Record<string, any>;
  createdAt?: Date;
  processedAt?: Date;
}

export interface RedditAnalytics {
  totalMentions: number;
  averageScore: number;
  topSubreddits: { name: string; count: number; avgScore: number }[];
  sentimentDistribution: { positive: number; neutral: number; negative: number };
  timeSeriesData: { date: string; mentions: number; avgScore: number }[];
}

export class RedditService {
  private redditAPI: RedditAPI;
  private productRepository: ProductRepository;
  private cacheService: CacheService;

  constructor() {
    this.redditAPI = new RedditAPI();
    this.productRepository = new PrismaProductRepository();
    this.cacheService = new CacheService();
  }

  async searchProductMentions(
    productName: string,
    options: RedditSearchOptions & { includeComments?: boolean } = {}
  ): Promise<ProductMention[]> {
    const cacheKey = createCacheKey(
      CACHE_KEYS.REDDIT,
      'mentions',
      productName,
      JSON.stringify(options)
    );

    const cached = await this.cacheService.get<ProductMention[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // Search for posts
      const postResults = await this.redditAPI.searchPosts(productName, options);
      const postMentions = await this.convertPostsToMentions(postResults.posts, productName);

      let commentMentions: ProductMention[] = [];

      // Optionally search comments
      if (options.includeComments) {
        const commentResults = await this.redditAPI.searchComments(productName, options);
        commentMentions = await this.convertCommentsToMentions(commentResults.comments, productName);
      }

      const allMentions = [...postMentions, ...commentMentions];

      await this.cacheService.set(cacheKey, allMentions, { ttl: CACHE_TTL.SHORT });

      return allMentions;
    } catch (error) {
      console.error('Error searching product mentions:', error);
      throw new Error('Failed to search product mentions on Reddit');
    }
  }

  async getProductMentionsFromDB(
    productId: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<{ mentions: ProductMention[]; total: number }> {
    const cacheKey = createCacheKey(
      CACHE_KEYS.REDDIT,
      'db_mentions',
      productId,
      JSON.stringify(pagination)
    );

    const cached = await this.cacheService.get<{ mentions: ProductMention[]; total: number }>(cacheKey);
    if (cached) {
      return cached;
    }

    const [mentions, total] = await Promise.all([
      prisma.productMention.findMany({
        where: { productId },
        orderBy: { createdAt: 'desc' },
        skip: ((pagination.page || 1) - 1) * (pagination.limit || 20),
        take: pagination.limit || 20,
      }),
      prisma.productMention.count({ where: { productId } })
    ]);

    const result = {
      mentions: mentions.map(mention => ({
        id: mention.id,
        productId: mention.productId,
        source: mention.source as 'REDDIT',
        sourceId: mention.sourceId,
        url: mention.url,
        content: mention.content,
        sentimentScore: mention.sentimentScore ? Number(mention.sentimentScore) : undefined,
        engagementMetrics: mention.engagementMetrics as Record<string, any>,
        authorInfo: mention.authorInfo as Record<string, any>,
        createdAt: mention.createdAt,
        processedAt: mention.processedAt,
      })),
      total
    };

    await this.cacheService.set(cacheKey, result, { ttl: CACHE_TTL.MEDIUM });

    return result;
  }

  async saveProductMentions(mentions: ProductMention[]): Promise<void> {
    if (mentions.length === 0) return;

    try {
      await prisma.productMention.createMany({
        data: mentions.map(mention => ({
          productId: mention.productId,
          source: mention.source,
          sourceId: mention.sourceId,
          content: mention.content,
          url: mention.url,
          sentimentScore: mention.sentimentScore,
          engagementMetrics: mention.engagementMetrics,
          authorInfo: mention.authorInfo,
          createdAt: mention.createdAt,
        })),
        skipDuplicates: true,
      });

      // Invalidate related caches
      await this.cacheService.deleteByPattern(`${CACHE_KEYS.REDDIT}:*`);

      console.log(`✅ Saved ${mentions.length} product mentions to database`);
    } catch (error) {
      console.error('Error saving product mentions:', error);
      throw new Error('Failed to save product mentions');
    }
  }

  async getRedditAnalytics(
    productId: string,
    timeframe: 'day' | 'week' | 'month' = 'week'
  ): Promise<RedditAnalytics> {
    const cacheKey = createCacheKey(CACHE_KEYS.REDDIT, 'analytics', productId, timeframe);

    const cached = await this.cacheService.get<RedditAnalytics>(cacheKey);
    if (cached) {
      return cached;
    }

    const timeframeDays = timeframe === 'day' ? 1 : timeframe === 'week' ? 7 : 30;
    const startDate = new Date(Date.now() - timeframeDays * 24 * 60 * 60 * 1000);

    const [totalMentions, avgScore, subredditStats, sentimentStats, timeSeriesData] = await Promise.all([
      this.getTotalMentions(productId, startDate),
      this.getAverageScore(productId, startDate),
      this.getTopSubreddits(productId, startDate),
      this.getSentimentDistribution(productId, startDate),
      this.getTimeSeriesData(productId, startDate, timeframe)
    ]);

    const analytics: RedditAnalytics = {
      totalMentions,
      averageScore: avgScore,
      topSubreddits: subredditStats,
      sentimentDistribution: sentimentStats,
      timeSeriesData
    };

    await this.cacheService.set(cacheKey, analytics, { ttl: CACHE_TTL.LONG });

    return analytics;
  }

  async monitorSubreddits(
    subreddits: string[],
    keywords: string[],
    options: { limit?: number; sort?: 'hot' | 'new' | 'top' } = {}
  ): Promise<{ subreddit: string; posts: RedditPost[] }[]> {
    const results = await Promise.allSettled(
      subreddits.map(async (subreddit) => {
        const posts = await this.redditAPI.getSubredditPosts(
          subreddit,
          options.sort || 'hot',
          options.limit || 25
        );

        // Filter posts that mention any of the keywords
        const relevantPosts = posts.filter(post =>
          keywords.some(keyword =>
            post.title.toLowerCase().includes(keyword.toLowerCase()) ||
            post.selftext.toLowerCase().includes(keyword.toLowerCase())
          )
        );

        return { subreddit, posts: relevantPosts };
      })
    );

    return results
      .filter((result): result is PromiseFulfilledResult<{ subreddit: string; posts: RedditPost[] }> =>
        result.status === 'fulfilled'
      )
      .map(result => result.value);
  }

  async getRedditHealthStatus(): Promise<{
    isHealthy: boolean;
    rateLimit: { remaining: number; resetTime: Date };
    lastError?: string;
  }> {
    try {
      const isHealthy = await this.redditAPI.healthCheck();
      const rateLimit = this.redditAPI.getRateLimitStatus();

      return {
        isHealthy,
        rateLimit,
      };
    } catch (error) {
      return {
        isHealthy: false,
        rateLimit: { remaining: 0, resetTime: new Date() },
        lastError: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async convertPostsToMentions(posts: RedditPost[], productName: string): Promise<ProductMention[]> {
    // Try to find matching products in database by searching
    const searchResults = await this.productRepository.search(
      { query: productName },
      { field: 'name', order: 'desc' },
      { page: 1, limit: 1 }
    );
    const productId = searchResults.products.length > 0 ? searchResults.products[0].id : 'unknown';

    return posts.map(post => ({
      id: `reddit_post_${post.id}`,
      productId,
      source: 'REDDIT' as const,
      sourceId: post.id,
      url: post.permalink,
      content: `${post.title}\n\n${post.selftext}`,
      sentimentScore: undefined,
      engagementMetrics: {
        score: post.score,
        commentCount: post.num_comments,
        upvoteRatio: post.upvote_ratio,
        subreddit: post.subreddit,
        over18: post.over_18,
        isSelf: post.is_self,
        originalUrl: post.url,
      },
      authorInfo: {
        username: post.author,
      },
      createdAt: new Date(post.created_utc * 1000),
    }));
  }

  private async convertCommentsToMentions(comments: RedditComment[], productName: string): Promise<ProductMention[]> {
    // Try to find matching products in database by searching
    const searchResults = await this.productRepository.search(
      { query: productName },
      { field: 'name', order: 'desc' },
      { page: 1, limit: 1 }
    );
    const productId = searchResults.products.length > 0 ? searchResults.products[0].id : 'unknown';

    return comments.map(comment => ({
      id: `reddit_comment_${comment.id}`,
      productId,
      source: 'REDDIT' as const,
      sourceId: comment.id,
      url: comment.permalink,
      content: comment.body,
      sentimentScore: undefined,
      engagementMetrics: {
        score: comment.score,
        parentId: comment.parent_id,
        linkId: comment.link_id,
        type: 'comment',
      },
      authorInfo: {
        username: comment.author,
      },
      createdAt: new Date(comment.created_utc * 1000),
    }));
  }

  private async getTotalMentions(productId: string, startDate: Date): Promise<number> {
    return await prisma.productMention.count({
      where: {
        productId,
        source: 'REDDIT',
        createdAt: { gte: startDate }
      }
    });
  }

  private async getAverageScore(productId: string, startDate: Date): Promise<number> {
    const result = await prisma.productMention.aggregate({
      where: {
        productId,
        source: 'REDDIT',
        createdAt: { gte: startDate }
      },
      _avg: { sentimentScore: true }
    });

    return Number(result._avg.sentimentScore) || 0;
  }

  private async getTopSubreddits(productId: string, startDate: Date): Promise<{ name: string; count: number; avgScore: number }[]> {
    // Since subreddit is not in the schema, we'll extract it from engagementMetrics
    const mentions = await prisma.productMention.findMany({
      where: {
        productId,
        source: 'REDDIT',
        createdAt: { gte: startDate }
      },
      select: {
        engagementMetrics: true,
        sentimentScore: true
      }
    });

    // Group by subreddit from engagementMetrics
    const subredditMap = new Map<string, { count: number; totalScore: number }>();

    mentions.forEach(mention => {
      const metrics = mention.engagementMetrics as any;
      const subreddit = metrics?.subreddit || 'Unknown';
      const score = Number(mention.sentimentScore) || 0;

      const existing = subredditMap.get(subreddit) || { count: 0, totalScore: 0 };
      subredditMap.set(subreddit, {
        count: existing.count + 1,
        totalScore: existing.totalScore + score
      });
    });

    return Array.from(subredditMap.entries())
      .map(([name, data]) => ({
        name,
        count: data.count,
        avgScore: data.count > 0 ? data.totalScore / data.count : 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  private async getSentimentDistribution(productId: string, startDate: Date): Promise<{ positive: number; neutral: number; negative: number }> {
    const sentiments = await prisma.productMention.findMany({
      where: {
        productId,
        source: 'REDDIT',
        createdAt: { gte: startDate },
        sentimentScore: { not: null }
      },
      select: { sentimentScore: true }
    });

    const distribution = { positive: 0, neutral: 0, negative: 0 };

    sentiments.forEach(({ sentimentScore }) => {
      if (sentimentScore === null) return;
      const score = Number(sentimentScore);
      if (score > 0.1) distribution.positive++;
      else if (score < -0.1) distribution.negative++;
      else distribution.neutral++;
    });

    return distribution;
  }

  private async getTimeSeriesData(
    productId: string,
    startDate: Date,
    timeframe: 'day' | 'week' | 'month'
  ): Promise<{ date: string; mentions: number; avgScore: number }[]> {
    // This is a simplified implementation - in production, you'd want more sophisticated time series aggregation
    const mentions = await prisma.productMention.findMany({
      where: {
        productId,
        source: 'REDDIT',
        createdAt: { gte: startDate }
      },
      select: { createdAt: true, sentimentScore: true },
      orderBy: { createdAt: 'asc' }
    });

    // Group by day for simplicity
    const groupedData = new Map<string, { count: number; totalScore: number }>();

    mentions.forEach(mention => {
      if (!mention.createdAt) return;
      const date = mention.createdAt.toISOString().split('T')[0];
      const score = Number(mention.sentimentScore) || 0;
      const existing = groupedData.get(date) || { count: 0, totalScore: 0 };
      groupedData.set(date, {
        count: existing.count + 1,
        totalScore: existing.totalScore + score
      });
    });

    return Array.from(groupedData.entries()).map(([date, data]) => ({
      date,
      mentions: data.count,
      avgScore: data.count > 0 ? data.totalScore / data.count : 0
    }));
  }
}
