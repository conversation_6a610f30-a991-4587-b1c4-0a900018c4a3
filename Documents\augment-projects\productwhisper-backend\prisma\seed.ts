import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Clear existing data
  await prisma.searchAnalytics.deleteMany();
  await prisma.productMention.deleteMany();
  await prisma.sentimentScore.deleteMany();
  await prisma.product.deleteMany();

  console.log('🗑️  Cleared existing data');

  // Create sample products
  const products = await Promise.all([
    prisma.product.create({
      data: {
        name: 'iPhone 15 Pro',
        description: 'Apple\'s latest flagship smartphone with titanium design and A17 Pro chip',
        category: 'Smartphones',
        brand: 'Apple',
        imageUrls: [
          'https://example.com/iphone15pro-1.jpg',
          'https://example.com/iphone15pro-2.jpg'
        ],
        externalIds: {
          amazon: 'B0C7R7T4HG',
          reddit: 'iphone_15_pro'
        },
        metadata: {
          price: 999,
          releaseDate: '2023-09-22',
          colors: ['Natural Titanium', 'Blue Titanium', 'White Titanium', 'Black Titanium'],
          storage: ['128GB', '256GB', '512GB', '1TB']
        }
      }
    }),
    prisma.product.create({
      data: {
        name: 'Steam Deck',
        description: 'Valve\'s handheld gaming PC with SteamOS',
        category: 'Gaming',
        brand: 'Valve',
        imageUrls: [
          'https://example.com/steamdeck-1.jpg',
          'https://example.com/steamdeck-2.jpg'
        ],
        externalIds: {
          steam: 'steamdeck',
          reddit: 'steam_deck'
        },
        metadata: {
          price: 399,
          releaseDate: '2022-02-25',
          storage: ['64GB', '256GB', '512GB'],
          display: '7-inch touchscreen'
        }
      }
    }),
    prisma.product.create({
      data: {
        name: 'AirPods Pro 2',
        description: 'Apple\'s premium wireless earbuds with active noise cancellation',
        category: 'Audio',
        brand: 'Apple',
        imageUrls: [
          'https://example.com/airpods-pro-2-1.jpg',
          'https://example.com/airpods-pro-2-2.jpg'
        ],
        externalIds: {
          amazon: 'B0BDHWDR12',
          reddit: 'airpods_pro_2'
        },
        metadata: {
          price: 249,
          releaseDate: '2022-09-23',
          features: ['Active Noise Cancellation', 'Transparency Mode', 'Spatial Audio'],
          batteryLife: '6 hours + 24 hours with case'
        }
      }
    }),
    prisma.product.create({
      data: {
        name: 'Sony WH-1000XM5',
        description: 'Sony\'s flagship noise-canceling wireless headphones',
        category: 'Audio',
        brand: 'Sony',
        imageUrls: [
          'https://example.com/sony-wh1000xm5-1.jpg',
          'https://example.com/sony-wh1000xm5-2.jpg'
        ],
        externalIds: {
          amazon: 'B09XS7JWHH',
          reddit: 'sony_wh1000xm5'
        },
        metadata: {
          price: 399,
          releaseDate: '2022-05-12',
          features: ['Industry-leading noise canceling', '30-hour battery life', 'Multipoint connection'],
          colors: ['Black', 'Silver']
        }
      }
    }),
    prisma.product.create({
      data: {
        name: 'MacBook Pro 14-inch M3',
        description: 'Apple\'s professional laptop with M3 chip',
        category: 'Laptops',
        brand: 'Apple',
        imageUrls: [
          'https://example.com/macbook-pro-14-m3-1.jpg',
          'https://example.com/macbook-pro-14-m3-2.jpg'
        ],
        externalIds: {
          amazon: 'B0CM5JV268',
          reddit: 'macbook_pro_m3'
        },
        metadata: {
          price: 1599,
          releaseDate: '2023-11-07',
          specs: {
            chip: 'Apple M3',
            memory: ['8GB', '16GB', '24GB'],
            storage: ['512GB', '1TB', '2TB', '4TB', '8TB'],
            display: '14.2-inch Liquid Retina XDR'
          },
          colors: ['Space Gray', 'Silver']
        }
      }
    })
  ]);

  console.log(`✅ Created ${products.length} products`);

  // Create sample product mentions
  const mentions = [];
  for (const product of products) {
    // Create mentions for each product
    const productMentions = await Promise.all([
      prisma.productMention.create({
        data: {
          productId: product.id,
          source: 'REDDIT',
          sourceId: `post_${Math.random().toString(36).substr(2, 9)}`,
          url: `https://reddit.com/r/technology/comments/example_${product.id}`,
          content: `Just got the ${product.name} - first impressions\n\nI've been using the ${product.name} for a week now and I'm really impressed. The build quality is excellent and it performs exactly as advertised. Highly recommend!`,
          sentimentScore: 0.7 + Math.random() * 0.3, // Positive sentiment
          engagementMetrics: {
            score: Math.floor(Math.random() * 100) + 50,
            commentCount: Math.floor(Math.random() * 50) + 10,
            subreddit: 'technology',
            upvoteRatio: 0.85 + Math.random() * 0.15,
            awards: Math.floor(Math.random() * 5)
          },
          authorInfo: {
            username: `user_${Math.random().toString(36).substr(2, 6)}`
          },
          createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Random date within last week
        }
      }),
      prisma.productMention.create({
        data: {
          productId: product.id,
          source: 'REDDIT',
          sourceId: `post_${Math.random().toString(36).substr(2, 9)}`,
          url: `https://reddit.com/r/reviews/comments/example_${product.id}`,
          content: `${product.name} review after 3 months\n\nMixed feelings about the ${product.name}. Some features are great but there are definitely some issues. The price point is a bit high for what you get.`,
          sentimentScore: -0.1 + Math.random() * 0.3, // Neutral to slightly positive
          engagementMetrics: {
            score: Math.floor(Math.random() * 30) + 5,
            commentCount: Math.floor(Math.random() * 25) + 5,
            subreddit: 'reviews',
            upvoteRatio: 0.65 + Math.random() * 0.2,
            awards: Math.floor(Math.random() * 2)
          },
          authorInfo: {
            username: `user_${Math.random().toString(36).substr(2, 6)}`
          },
          createdAt: new Date(Date.now() - Math.random() * 14 * 24 * 60 * 60 * 1000), // Random date within last 2 weeks
        }
      }),
      prisma.productMention.create({
        data: {
          productId: product.id,
          source: 'REDDIT',
          sourceId: `post_${Math.random().toString(36).substr(2, 9)}`,
          url: `https://reddit.com/r/gadgets/comments/example_${product.id}`,
          content: `Disappointed with ${product.name}\n\nI had high expectations for the ${product.name} but it's been nothing but problems. Quality control seems poor and customer service is unhelpful. Would not recommend.`,
          sentimentScore: -0.8 + Math.random() * 0.3, // Negative sentiment
          engagementMetrics: {
            score: Math.floor(Math.random() * 20) + 1,
            commentCount: Math.floor(Math.random() * 40) + 15,
            subreddit: 'gadgets',
            upvoteRatio: 0.45 + Math.random() * 0.3,
            awards: 0
          },
          authorInfo: {
            username: `user_${Math.random().toString(36).substr(2, 6)}`
          },
          createdAt: new Date(Date.now() - Math.random() * 21 * 24 * 60 * 60 * 1000), // Random date within last 3 weeks
        }
      })
    ]);
    mentions.push(...productMentions);
  }

  console.log(`✅ Created ${mentions.length} product mentions`);

  // Create sentiment scores for products
  for (const product of products) {
    const productMentions = mentions.filter(m => m.productId === product.id);
    const sentiments = productMentions.map(m => Number(m.sentimentScore) || 0);

    const overallScore = sentiments.reduce((sum, s) => sum + s, 0) / sentiments.length;
    const positiveCount = sentiments.filter(s => s > 0.1).length;
    const negativeCount = sentiments.filter(s => s < -0.1).length;
    const neutralCount = sentiments.length - positiveCount - negativeCount;

    await prisma.sentimentScore.create({
      data: {
        productId: product.id,
        source: 'REDDIT',
        overallScore,
        confidenceScore: Math.min(0.9, sentiments.length / 10), // Higher confidence with more data
        sampleSize: sentiments.length,
        positiveScore: positiveCount / sentiments.length,
        negativeScore: negativeCount / sentiments.length,
        neutralScore: neutralCount / sentiments.length
      }
    });
  }

  console.log(`✅ Created sentiment scores for ${products.length} products`);

  // Create sample search analytics
  const searchQueries = [
    'iPhone 15 Pro review',
    'Steam Deck vs Nintendo Switch',
    'AirPods Pro 2 noise cancellation',
    'Sony WH-1000XM5 vs Bose',
    'MacBook Pro M3 performance',
    'best gaming laptop 2024',
    'wireless headphones comparison',
    'smartphone camera quality',
    'laptop for programming',
    'noise canceling earbuds'
  ];

  for (const query of searchQueries) {
    await prisma.searchAnalytics.create({
      data: {
        query,
        resultsCount: Math.floor(Math.random() * 100) + 10,
        ipAddress: null, // Anonymous
        userAgent: null, // Anonymous
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Random date within last month
      }
    });
  }

  console.log(`✅ Created search analytics for ${searchQueries.length} queries`);

  // Print summary
  const totalProducts = await prisma.product.count();
  const totalMentions = await prisma.productMention.count();
  const totalSentimentScores = await prisma.sentimentScore.count();
  const totalSearchAnalytics = await prisma.searchAnalytics.count();

  console.log('\n📊 Database seeding completed!');
  console.log(`   Products: ${totalProducts}`);
  console.log(`   Mentions: ${totalMentions}`);
  console.log(`   Sentiment Scores: ${totalSentimentScores}`);
  console.log(`   Search Analytics: ${totalSearchAnalytics}`);
  console.log('\n🚀 Ready to start the application!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
