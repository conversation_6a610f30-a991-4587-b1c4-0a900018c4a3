import { ProductRepository, PrismaProductRepository } from '@/core/repositories/ProductRepository';
import { ProductEntity } from '@/core/entities/Product';
import { CacheService } from '@/infrastructure/cache/CacheService';
import { prisma } from '@/infrastructure/database/prisma';
import { CACHE_KEYS, CACHE_TTL } from '@/shared/constants';
import { createCacheKey } from '@/shared/utils';
import { PaginationParams } from '@/shared/types';

export interface SearchFilters {
  sources?: string[];
  sentiment?: 'positive' | 'negative' | 'neutral';
  dateFrom?: Date;
  dateTo?: Date;
  category?: string;
  brand?: string;
}

export interface SearchResult {
  products: ProductEntity[];
  total: number;
  suggestions?: string[];
  facets?: {
    categories: { name: string; count: number }[];
    brands: { name: string; count: number }[];
    sources: { name: string; count: number }[];
  };
}

export class SearchService {
  private productRepository: ProductRepository;
  private cacheService: CacheService;

  constructor() {
    this.productRepository = new PrismaProductRepository();
    this.cacheService = new CacheService();
  }

  async globalSearch(
    query: string,
    filters: SearchFilters = {},
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<SearchResult> {
    // Create cache key
    const cacheKey = createCacheKey(
      CACHE_KEYS.SEARCH,
      'global',
      JSON.stringify({ query, filters, pagination })
    );

    // Try cache first
    const cached = await this.cacheService.get<SearchResult>(cacheKey);
    if (cached) {
      return cached;
    }

    // Track search analytics
    await this.trackSearch(query);

    // Build search conditions
    const searchConditions = this.buildSearchConditions(query, filters);

    // Execute search
    const [products, total, facets] = await Promise.all([
      this.searchProducts(searchConditions, pagination),
      this.countSearchResults(searchConditions),
      this.getFacets(searchConditions)
    ]);

    // Generate suggestions if no results
    const suggestions = total === 0 ? await this.generateSuggestions(query) : undefined;

    const result: SearchResult = {
      products,
      total,
      suggestions,
      facets
    };

    // Cache result
    await this.cacheService.set(cacheKey, result, { ttl: CACHE_TTL.SHORT });

    return result;
  }

  async getSearchSuggestions(query: string, limit: number = 10): Promise<string[]> {
    const cacheKey = createCacheKey(CACHE_KEYS.SEARCH, 'suggestions', query);
    
    const cached = await this.cacheService.get<string[]>(cacheKey);
    if (cached) {
      return cached;
    }

    // Get suggestions from product names and categories
    const suggestions = await prisma.$queryRaw<{ suggestion: string }[]>`
      SELECT DISTINCT name as suggestion
      FROM products 
      WHERE name ILIKE ${`%${query}%`}
      UNION
      SELECT DISTINCT category as suggestion
      FROM products 
      WHERE category ILIKE ${`%${query}%`} AND category IS NOT NULL
      UNION
      SELECT DISTINCT brand as suggestion
      FROM products 
      WHERE brand ILIKE ${`%${query}%`} AND brand IS NOT NULL
      ORDER BY suggestion
      LIMIT ${limit}
    `;

    const result = suggestions.map(s => s.suggestion);
    
    await this.cacheService.set(cacheKey, result, { ttl: CACHE_TTL.MEDIUM });
    
    return result;
  }

  async getRecentSearches(limit: number = 10): Promise<{ query: string; count: number }[]> {
    const cacheKey = createCacheKey(CACHE_KEYS.SEARCH, 'recent');
    
    const cached = await this.cacheService.get<{ query: string; count: number }[]>(cacheKey);
    if (cached) {
      return cached;
    }

    const recentSearches = await prisma.searchAnalytics.groupBy({
      by: ['query'],
      _count: { query: true },
      orderBy: { _count: { query: 'desc' } },
      where: {
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
        }
      },
      take: limit
    });

    const result = recentSearches.map(search => ({
      query: search.query,
      count: search._count.query
    }));

    await this.cacheService.set(cacheKey, result, { ttl: CACHE_TTL.MEDIUM });
    
    return result;
  }

  async getPopularSearches(
    timeframe: 'day' | 'week' | 'month' = 'week',
    limit: number = 10
  ): Promise<{ query: string; count: number }[]> {
    const cacheKey = createCacheKey(CACHE_KEYS.SEARCH, 'popular', timeframe);
    
    const cached = await this.cacheService.get<{ query: string; count: number }[]>(cacheKey);
    if (cached) {
      return cached;
    }

    const timeframeDays = timeframe === 'day' ? 1 : timeframe === 'week' ? 7 : 30;
    
    const popularSearches = await prisma.searchAnalytics.groupBy({
      by: ['query'],
      _count: { query: true },
      orderBy: { _count: { query: 'desc' } },
      where: {
        createdAt: {
          gte: new Date(Date.now() - timeframeDays * 24 * 60 * 60 * 1000)
        }
      },
      take: limit
    });

    const result = popularSearches.map(search => ({
      query: search.query,
      count: search._count.query
    }));

    await this.cacheService.set(cacheKey, result, { ttl: CACHE_TTL.LONG });
    
    return result;
  }

  private buildSearchConditions(query: string, filters: SearchFilters): any {
    const conditions: any = {
      AND: []
    };

    // Full-text search on name and description
    if (query) {
      conditions.AND.push({
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { category: { contains: query, mode: 'insensitive' } },
          { brand: { contains: query, mode: 'insensitive' } }
        ]
      });
    }

    // Apply filters
    if (filters.category) {
      conditions.AND.push({ category: { contains: filters.category, mode: 'insensitive' } });
    }

    if (filters.brand) {
      conditions.AND.push({ brand: { contains: filters.brand, mode: 'insensitive' } });
    }

    if (filters.dateFrom || filters.dateTo) {
      const dateFilter: any = {};
      if (filters.dateFrom) dateFilter.gte = filters.dateFrom;
      if (filters.dateTo) dateFilter.lte = filters.dateTo;
      conditions.AND.push({ createdAt: dateFilter });
    }

    // Sentiment filter (requires join with sentiment scores)
    if (filters.sentiment) {
      conditions.AND.push({
        sentimentScores: {
          some: {
            overallScore: this.getSentimentRange(filters.sentiment)
          }
        }
      });
    }

    return conditions.AND.length > 0 ? conditions : {};
  }

  private getSentimentRange(sentiment: 'positive' | 'negative' | 'neutral'): any {
    switch (sentiment) {
      case 'positive':
        return { gt: 0.1 };
      case 'negative':
        return { lt: -0.1 };
      case 'neutral':
        return { gte: -0.1, lte: 0.1 };
      default:
        return {};
    }
  }

  private async searchProducts(conditions: any, pagination: PaginationParams): Promise<ProductEntity[]> {
    const products = await prisma.product.findMany({
      where: conditions,
      orderBy: { createdAt: 'desc' },
      skip: ((pagination.page || 1) - 1) * (pagination.limit || 20),
      take: pagination.limit || 20,
      include: {
        sentimentScores: true,
        _count: {
          select: { mentions: true }
        }
      }
    });

    return products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      category: product.category,
      brand: product.brand,
      imageUrls: product.imageUrls,
      externalIds: product.externalIds as Record<string, any>,
      metadata: product.metadata as Record<string, any>,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    }));
  }

  private async countSearchResults(conditions: any): Promise<number> {
    return await prisma.product.count({ where: conditions });
  }

  private async getFacets(conditions: any): Promise<SearchResult['facets']> {
    const [categories, brands] = await Promise.all([
      prisma.product.groupBy({
        by: ['category'],
        _count: { category: true },
        where: { ...conditions, category: { not: null } },
        orderBy: { _count: { category: 'desc' } },
        take: 10
      }),
      prisma.product.groupBy({
        by: ['brand'],
        _count: { brand: true },
        where: { ...conditions, brand: { not: null } },
        orderBy: { _count: { brand: 'desc' } },
        take: 10
      })
    ]);

    return {
      categories: categories.map(c => ({ name: c.category || 'Unknown', count: c._count.category })),
      brands: brands.map(b => ({ name: b.brand || 'Unknown', count: b._count.brand })),
      sources: [] // TODO: Implement source facets
    };
  }

  private async generateSuggestions(query: string): Promise<string[]> {
    // Simple suggestion algorithm - in production, use more sophisticated methods
    const similarProducts = await prisma.product.findMany({
      where: {
        OR: [
          { name: { contains: query.substring(0, Math.max(3, query.length - 2)), mode: 'insensitive' } },
          { category: { contains: query.substring(0, Math.max(3, query.length - 2)), mode: 'insensitive' } }
        ]
      },
      select: { name: true, category: true },
      take: 5
    });

    const suggestions = new Set<string>();
    similarProducts.forEach(product => {
      if (product.name) suggestions.add(product.name);
      if (product.category) suggestions.add(product.category);
    });

    return Array.from(suggestions).slice(0, 5);
  }

  private async trackSearch(query: string): Promise<void> {
    try {
      await prisma.searchAnalytics.create({
        data: {
          query: query.toLowerCase().trim(),
          resultsCount: 0, // Will be updated after search
          ipAddress: null, // TODO: Get from request context
          userAgent: null  // TODO: Get from request context
        }
      });
    } catch (error) {
      // Don't fail search if analytics tracking fails
      console.error('Failed to track search:', error);
    }
  }
}
