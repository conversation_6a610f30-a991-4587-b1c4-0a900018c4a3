# ProductWhisper Backend

A production-ready Node.js/TypeScript backend for ProductWhisper - a platform that helps users discover products through sentiment analysis of reviews and discussions from various sources. This backend provides public APIs without authentication requirements.

## Technology Stack

- **Fastify + TypeScript**: High-performance API server
- **Prisma ORM**: Type-safe database operations
- **PostgreSQL 15+**: Optimized database schema
- **Redis 7+**: Multi-layer caching
- **Zod**: Runtime type validation
- **Winston**: Structured logging
- **Socket.io**: Real-time features
- **FastAPI**: Python sentiment analysis service
- **Docker**: Containerized deployment

## Project Structure

```
productwhisper-backend/
├── src/
│   ├── api/
│   │   ├── controllers/      # Request handlers
│   │   ├── middleware/       # Fastify middleware
│   │   ├── routes/          # API route definitions
│   │   ├── validators/      # Request validation schemas
│   │   └── plugins/         # Fastify plugins setup
│   ├── core/
│   │   ├── entities/        # Domain entities
│   │   ├── repositories/    # Data access layer
│   │   ├── services/        # Business logic
│   │   └── use-cases/       # Application use cases
│   ├── infrastructure/
│   │   ├── database/        # Database configuration
│   │   ├── cache/          # Redis cache implementation
│   │   ├── external-apis/   # External API integrations
│   │   ├── messaging/       # Message queue setup
│   │   └── monitoring/      # Metrics and monitoring
│   ├── shared/
│   │   ├── constants/       # Application constants
│   │   ├── types/          # TypeScript type definitions
│   │   ├── utils/          # Utility functions
│   │   └── errors/         # Error handling
│   ├── config/             # Configuration management
│   └── tests/              # Test utilities
├── python-services/
│   └── sentiment-analysis/  # FastAPI sentiment service
├── tests/                   # Test files
├── scripts/                 # Database and deployment scripts
├── docs/                    # Documentation
└── docker/                  # Docker configurations
```

## Getting Started

### Prerequisites

- **Node.js** (v18+)
- **PostgreSQL** (v15+)
- **Redis** (v7+)
- **Python** (v3.11+)
- **Docker** (optional, for containerized development)

### Quick Start with Docker

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd productwhisper-backend
   ```

2. **Copy environment file**
   ```bash
   cp .env.example .env
   ```

3. **Start all services with Docker**
   ```bash
   docker-compose up -d
   ```

4. **Check service health**
   ```bash
   curl http://localhost:8000/health
   ```

### Manual Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start PostgreSQL and Redis**
   ```bash
   # Using Docker
   docker run -d --name postgres -p 5432:5432 -e POSTGRES_PASSWORD=productwhisper123 postgres:15-alpine
   docker run -d --name redis -p 6379:6379 redis:7-alpine
   ```

4. **Generate Prisma client and run migrations**
   ```bash
   npm run db:generate
   npm run db:migrate
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

## API Documentation

Once the server is running, you can access:

- **API Documentation**: http://localhost:8000/docs (Swagger UI)
- **Health Check**: http://localhost:8000/health
- **API Base**: http://localhost:8000/api/v1

## Available Scripts

```bash
# Development
npm run dev              # Start development server with hot reload
npm run build           # Build for production
npm run start           # Start production server

# Database
npm run db:generate     # Generate Prisma client
npm run db:migrate      # Run database migrations
npm run db:seed         # Seed database with sample data
npm run db:studio       # Open Prisma Studio
npm run db:reset        # Reset database (development only)

# Testing
npm test                # Run tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Run tests with coverage report

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint issues
npm run format          # Format code with Prettier

# Docker
npm run docker:build    # Build Docker images
npm run docker:up       # Start services with Docker Compose
npm run docker:down     # Stop Docker services
npm run docker:logs     # View Docker logs
```

## API Endpoints

**Note**: All endpoints are public and do not require authentication.

### Products (`/api/v1/products`)
- `GET /` - Search and filter products
- `GET /:id` - Get product details
- `GET /:id/sentiment` - Get product with sentiment analysis
- `GET /:id/mentions` - Get product mentions/reviews
- `POST /` - Create new product
- `PUT /:id` - Update product
- `DELETE /:id` - Delete product
- `GET /meta/categories` - Get product categories
- `GET /meta/brands` - Get product brands
- `GET /recent` - Get recently added products
- `GET /popular` - Get popular products

### Search (`/api/v1/search`)
- `GET /` - Global product search
- `GET /suggestions` - Get search suggestions
- `GET /recent` - Get recent searches
- `GET /popular` - Get popular searches

### Sentiment Analysis (`/api/v1/sentiment`)
- `POST /analyze` - Analyze sentiment of text
- `POST /analyze/batch` - Batch sentiment analysis
- `GET /product/:id/history` - Get sentiment history
- `GET /realtime` - Real-time sentiment updates (WebSocket)

### Trends (`/api/v1/trends`)
- `GET /products` - Get trending products
- `GET /categories` - Get trending categories
- `GET /keywords` - Get trending keywords
- `GET /sentiment` - Get sentiment trends

### Reddit Integration (`/api/v1/reddit`)
- `GET /search` - Search Reddit for product mentions
- `GET /trending` - Get trending Reddit posts
- `GET /post/:id` - Get Reddit post details
- `GET /subreddit/:name` - Get subreddit information
- `GET /product/:id/mentions` - Get product mentions from Reddit

### Chat/FAQ (`/api/v1/chat`)
- `POST /message` - Send message to chatbot
- `GET /history/:sessionId` - Get chat history
- `GET /faq/categories` - Get FAQ categories
- `GET /faq/category/:categoryId` - Get FAQ questions by category
- `GET /faq/search` - Search FAQ
- `GET /realtime` - Real-time chat (WebSocket)

### Product Comparison (`/api/v1/comparison`)
- `POST /compare` - Compare multiple products
- `GET /history` - Get comparison history
- `GET /saved` - Get saved comparisons
- `POST /save` - Save a comparison
- `GET /:id` - Get comparison by ID
- `DELETE /:id` - Delete saved comparison
- `GET /suggestions/:productId` - Get comparison suggestions
- `POST /:id/report` - Generate comparison report

### Analytics (`/api/v1/analytics`)
- `GET /overview` - Get analytics overview
- `GET /search` - Get search analytics
- `GET /products` - Get product analytics
- `GET /sentiment` - Get sentiment analytics
- `GET /behavior` - Get user behavior analytics
- `GET /realtime` - Get real-time analytics
- `POST /export` - Export analytics data
- `GET /report/:id` - Get analytics report
- `GET /realtime/stream` - Real-time analytics stream (WebSocket)

## License

This project is licensed under the MIT License.
