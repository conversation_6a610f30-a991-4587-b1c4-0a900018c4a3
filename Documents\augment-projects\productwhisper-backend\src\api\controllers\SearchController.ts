import { FastifyRequest, FastifyReply } from 'fastify';
import { SearchService, SearchFilters } from '@/core/services/SearchService';
import { PaginationSchema } from '@/shared/types';
import { ValidationError } from '@/shared/errors';
import { z } from 'zod';

const SearchQuerySchema = z.object({
  q: z.string().min(1).max(200),
  sources: z.array(z.string()).optional(),
  sentiment: z.enum(['positive', 'negative', 'neutral']).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  category: z.string().optional(),
  brand: z.string().optional(),
});

const SuggestionsQuerySchema = z.object({
  q: z.string().min(1).max(100),
  limit: z.coerce.number().min(1).max(20).default(10),
});

const PopularSearchesQuerySchema = z.object({
  timeframe: z.enum(['day', 'week', 'month']).default('week'),
  limit: z.coerce.number().min(1).max(50).default(10),
});

export class SearchController {
  private searchService: SearchService;

  constructor() {
    this.searchService = new SearchService();
  }

  async globalSearch(request: FastifyRequest, reply: FastifyReply) {
    // Validate query parameters
    const queryValidation = SearchQuerySchema.merge(PaginationSchema).safeParse(request.query);

    if (!queryValidation.success) {
      throw new ValidationError('Invalid search parameters', queryValidation.error.errors);
    }

    const { q, sources, sentiment, dateFrom, dateTo, category, brand, page, limit } = queryValidation.data;

    // Build filters
    const filters: SearchFilters = {
      ...(sources && { sources }),
      ...(sentiment && { sentiment }),
      ...(dateFrom && { dateFrom: new Date(dateFrom) }),
      ...(dateTo && { dateTo: new Date(dateTo) }),
      ...(category && { category }),
      ...(brand && { brand }),
    };

    // Build pagination
    const pagination = { page, limit };

    // Perform search
    const result = await this.searchService.globalSearch(q, filters, pagination);

    // Set additional metadata in response
    reply.header('X-Search-Query', q);
    reply.header('X-Search-Suggestions-Count', result.suggestions?.length || 0);

    return reply.paginated(
      result.products,
      page,
      limit,
      result.total,
      'Search completed successfully'
    );
  }

  async getSearchSuggestions(request: FastifyRequest, reply: FastifyReply) {
    // Validate query parameters
    const queryValidation = SuggestionsQuerySchema.safeParse(request.query);

    if (!queryValidation.success) {
      throw new ValidationError('Invalid suggestion parameters', queryValidation.error.errors);
    }

    const { q, limit } = queryValidation.data;

    const suggestions = await this.searchService.getSearchSuggestions(q, limit);

    return reply.success(
      { suggestions, query: q },
      'Search suggestions retrieved successfully'
    );
  }

  async getRecentSearches(request: FastifyRequest, reply: FastifyReply) {
    const query = request.query as { limit?: number };
    const limit = Math.min(query.limit || 10, 50);

    const recentSearches = await this.searchService.getRecentSearches(limit);

    return reply.success(
      { searches: recentSearches },
      'Recent searches retrieved successfully'
    );
  }

  async getPopularSearches(request: FastifyRequest, reply: FastifyReply) {
    // Validate query parameters
    const queryValidation = PopularSearchesQuerySchema.safeParse(request.query);

    if (!queryValidation.success) {
      throw new ValidationError('Invalid popular searches parameters', queryValidation.error.errors);
    }

    const { timeframe, limit } = queryValidation.data;

    const popularSearches = await this.searchService.getPopularSearches(timeframe, limit);

    return reply.success(
      {
        searches: popularSearches,
        timeframe,
        period: this.getTimeframePeriod(timeframe)
      },
      'Popular searches retrieved successfully'
    );
  }

  async getSearchAnalytics(request: FastifyRequest, reply: FastifyReply) {
    // Basic search analytics - can be expanded
    const [recent, popular] = await Promise.all([
      this.searchService.getRecentSearches(5),
      this.searchService.getPopularSearches('week', 5)
    ]);

    const analytics = {
      recentSearches: recent,
      popularSearches: popular,
      trends: {
        // TODO: Implement trending search terms
        trending: [],
        emerging: []
      },
      summary: {
        totalSearchesToday: 0, // TODO: Implement
        averageResultsPerSearch: 0, // TODO: Implement
        topCategories: [], // TODO: Implement
        topBrands: [] // TODO: Implement
      }
    };

    return reply.success(analytics, 'Search analytics retrieved successfully');
  }

  private getTimeframePeriod(timeframe: 'day' | 'week' | 'month'): string {
    const now = new Date();
    switch (timeframe) {
      case 'day':
        return `Last 24 hours (since ${new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString()})`;
      case 'week':
        return `Last 7 days (since ${new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString()})`;
      case 'month':
        return `Last 30 days (since ${new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString()})`;
      default:
        return 'Unknown period';
    }
  }
}
