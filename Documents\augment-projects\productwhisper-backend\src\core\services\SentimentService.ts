import axios, { AxiosInstance } from 'axios';
import { externalServicesConfig } from '@/config';
import { CacheService } from '@/infrastructure/cache/CacheService';
import { prisma } from '@/infrastructure/database/prisma';
import { CACHE_KEYS, CACHE_TTL } from '@/shared/constants';
import { createCacheKey } from '@/shared/utils';

export interface SentimentAnalysisRequest {
  text: string;
  language?: string;
  context?: 'product_review' | 'social_media' | 'comment' | 'general';
}

export interface SentimentAnalysisResult {
  overallScore: number; // -1 to 1
  confidence: number; // 0 to 1
  label: 'positive' | 'negative' | 'neutral';
  scores: {
    positive: number;
    negative: number;
    neutral: number;
  };
  emotions?: {
    joy: number;
    anger: number;
    fear: number;
    sadness: number;
    surprise: number;
    disgust: number;
  };
  aspects?: {
    aspect: string;
    sentiment: number;
    confidence: number;
  }[];
}

export interface BatchSentimentRequest {
  texts: string[];
  language?: string;
  context?: 'product_review' | 'social_media' | 'comment' | 'general';
}

export interface BatchSentimentResult {
  results: SentimentAnalysisResult[];
  processingTime: number;
  totalTexts: number;
  successCount: number;
  errorCount: number;
}

export interface SentimentTrends {
  productId: string;
  timeframe: 'day' | 'week' | 'month';
  data: {
    date: string;
    averageSentiment: number;
    mentionCount: number;
    positiveCount: number;
    negativeCount: number;
    neutralCount: number;
  }[];
  summary: {
    overallTrend: 'improving' | 'declining' | 'stable';
    averageSentiment: number;
    totalMentions: number;
    sentimentDistribution: {
      positive: number;
      negative: number;
      neutral: number;
    };
  };
}

export class SentimentService {
  private client: AxiosInstance;
  private cacheService: CacheService;

  constructor() {
    this.client = axios.create({
      baseURL: externalServicesConfig.sentimentApiUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.cacheService = new CacheService();
    this.setupInterceptors();
  }

  private setupInterceptors() {
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('Sentiment API error:', error.response?.data || error.message);
        throw new Error(`Sentiment analysis failed: ${error.response?.data?.error || error.message}`);
      }
    );
  }

  async analyzeSentiment(request: SentimentAnalysisRequest): Promise<SentimentAnalysisResult> {
    // Create cache key based on text and context
    const cacheKey = createCacheKey(
      CACHE_KEYS.SENTIMENT,
      'single',
      this.hashText(request.text),
      request.context || 'general'
    );

    // Try cache first
    const cached = await this.cacheService.get<SentimentAnalysisResult>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await this.client.post('/analyze', request);
      const result = this.normalizeSentimentResult(response.data);

      // Cache the result
      await this.cacheService.set(cacheKey, result, { ttl: CACHE_TTL.LONG });

      return result;
    } catch (error) {
      console.error('Sentiment analysis error:', error);

      // Return fallback sentiment analysis
      return this.getFallbackSentiment(request.text);
    }
  }

  async analyzeBatchSentiment(request: BatchSentimentRequest): Promise<BatchSentimentResult> {
    const startTime = Date.now();

    try {
      const response = await this.client.post('/analyze-batch', request);
      const processingTime = Date.now() - startTime;

      const results = response.data.results.map((result: any) =>
        this.normalizeSentimentResult(result)
      );

      return {
        results,
        processingTime,
        totalTexts: request.texts.length,
        successCount: results.length,
        errorCount: request.texts.length - results.length,
      };
    } catch (error) {
      console.error('Batch sentiment analysis error:', error);

      // Fallback to individual analysis
      return await this.fallbackBatchAnalysis(request);
    }
  }

  async analyzeProductMentions(productId: string): Promise<void> {
    try {
      // Get unanalyzed mentions
      const mentions = await prisma.productMention.findMany({
        where: {
          productId,
          sentimentScore: null,
        },
        take: 100, // Process in batches
      });

      if (mentions.length === 0) {
        return;
      }

      // Prepare batch request
      const batchRequest: BatchSentimentRequest = {
        texts: mentions.map(mention => mention.content),
        context: 'social_media',
      };

      // Analyze sentiment
      const batchResult = await this.analyzeBatchSentiment(batchRequest);

      // Update mentions with sentiment scores
      const updates = mentions.map((mention, index) => {
        const sentiment = batchResult.results[index];
        return prisma.productMention.update({
          where: { id: mention.id },
          data: {
            sentimentScore: sentiment?.overallScore || 0,
          },
        });
      });

      await Promise.all(updates);

      // Update product sentiment scores
      await this.updateProductSentimentScores(productId);

      console.log(`✅ Analyzed sentiment for ${mentions.length} mentions of product ${productId}`);
    } catch (error) {
      console.error('Error analyzing product mentions:', error);
      throw new Error('Failed to analyze product mention sentiments');
    }
  }

  async getSentimentTrends(
    productId: string,
    timeframe: 'day' | 'week' | 'month' = 'week'
  ): Promise<SentimentTrends> {
    const cacheKey = createCacheKey(CACHE_KEYS.SENTIMENT, 'trends', productId, timeframe);

    const cached = await this.cacheService.get<SentimentTrends>(cacheKey);
    if (cached) {
      return cached;
    }

    const timeframeDays = timeframe === 'day' ? 1 : timeframe === 'week' ? 7 : 30;
    const startDate = new Date(Date.now() - timeframeDays * 24 * 60 * 60 * 1000);

    // Get mentions with sentiment data
    const mentions = await prisma.productMention.findMany({
      where: {
        productId,
        createdAt: { gte: startDate },
        sentimentScore: { not: null },
      },
      select: {
        createdAt: true,
        sentimentScore: true,
      },
      orderBy: { createdAt: 'asc' },
    });

    // Group by date
    const groupedData = new Map<string, { sentiments: number[]; count: number }>();

    mentions.forEach(mention => {
      const date = mention.createdAt.toISOString().split('T')[0];
      const existing = groupedData.get(date) || { sentiments: [], count: 0 };
      existing.sentiments.push(Number(mention.sentimentScore!));
      existing.count++;
      groupedData.set(date, existing);
    });

    // Calculate daily statistics
    const data = Array.from(groupedData.entries()).map(([date, dayData]) => {
      const averageSentiment = dayData.sentiments.reduce((sum, s) => sum + s, 0) / dayData.sentiments.length;
      const positiveCount = dayData.sentiments.filter(s => s > 0.1).length;
      const negativeCount = dayData.sentiments.filter(s => s < -0.1).length;
      const neutralCount = dayData.count - positiveCount - negativeCount;

      return {
        date,
        averageSentiment,
        mentionCount: dayData.count,
        positiveCount,
        negativeCount,
        neutralCount,
      };
    });

    // Calculate summary
    const allSentiments = mentions.map(m => Number(m.sentimentScore!));
    const averageSentiment = allSentiments.reduce((sum, s) => sum + s, 0) / allSentiments.length;
    const positiveTotal = allSentiments.filter(s => s > 0.1).length;
    const negativeTotal = allSentiments.filter(s => s < -0.1).length;
    const neutralTotal = allSentiments.length - positiveTotal - negativeTotal;

    // Determine trend
    const recentAvg = data.slice(-3).reduce((sum, d) => sum + d.averageSentiment, 0) / Math.min(3, data.length);
    const earlierAvg = data.slice(0, 3).reduce((sum, d) => sum + d.averageSentiment, 0) / Math.min(3, data.length);
    const trendDiff = recentAvg - earlierAvg;

    let overallTrend: 'improving' | 'declining' | 'stable' = 'stable';
    if (trendDiff > 0.1) overallTrend = 'improving';
    else if (trendDiff < -0.1) overallTrend = 'declining';

    const trends: SentimentTrends = {
      productId,
      timeframe,
      data,
      summary: {
        overallTrend,
        averageSentiment,
        totalMentions: mentions.length,
        sentimentDistribution: {
          positive: positiveTotal,
          negative: negativeTotal,
          neutral: neutralTotal,
        },
      },
    };

    await this.cacheService.set(cacheKey, trends, { ttl: CACHE_TTL.MEDIUM });

    return trends;
  }

  async getProductSentimentSummary(productId: string): Promise<{
    overallScore: number;
    confidence: number;
    totalMentions: number;
    distribution: { positive: number; negative: number; neutral: number };
    lastUpdated: Date;
  }> {
    const sentimentScore = await prisma.sentimentScore.findFirst({
      where: { productId },
      orderBy: { lastUpdated: 'desc' },
    });

    if (!sentimentScore) {
      return {
        overallScore: 0,
        confidence: 0,
        totalMentions: 0,
        distribution: { positive: 0, negative: 0, neutral: 0 },
        lastUpdated: new Date(),
      };
    }

    return {
      overallScore: Number(sentimentScore.overallScore),
      confidence: Number(sentimentScore.confidenceScore),
      totalMentions: sentimentScore.sampleSize,
      distribution: {
        positive: Math.round(Number(sentimentScore.positiveScore) * sentimentScore.sampleSize),
        negative: Math.round(Number(sentimentScore.negativeScore) * sentimentScore.sampleSize),
        neutral: Math.round(Number(sentimentScore.neutralScore) * sentimentScore.sampleSize),
      },
      lastUpdated: sentimentScore.lastUpdated,
    };
  }

  private normalizeSentimentResult(data: any): SentimentAnalysisResult {
    // Normalize the response from the sentiment API
    return {
      overallScore: data.overall_score || data.score || 0,
      confidence: data.confidence || 0.5,
      label: data.label || this.scoreToLabel(data.overall_score || data.score || 0),
      scores: {
        positive: data.scores?.positive || data.positive || 0,
        negative: data.scores?.negative || data.negative || 0,
        neutral: data.scores?.neutral || data.neutral || 0,
      },
      emotions: data.emotions,
      aspects: data.aspects,
    };
  }

  private scoreToLabel(score: number): 'positive' | 'negative' | 'neutral' {
    if (score > 0.1) return 'positive';
    if (score < -0.1) return 'negative';
    return 'neutral';
  }

  private getFallbackSentiment(text: string): SentimentAnalysisResult {
    // Simple keyword-based fallback sentiment analysis
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'best', 'awesome', 'fantastic'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'worst', 'horrible', 'disappointing'];

    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;

    let score = 0;
    if (positiveCount > negativeCount) score = 0.3;
    else if (negativeCount > positiveCount) score = -0.3;

    return {
      overallScore: score,
      confidence: 0.3, // Low confidence for fallback
      label: this.scoreToLabel(score),
      scores: {
        positive: positiveCount > 0 ? 0.6 : 0.2,
        negative: negativeCount > 0 ? 0.6 : 0.2,
        neutral: 0.4,
      },
    };
  }

  private async fallbackBatchAnalysis(request: BatchSentimentRequest): Promise<BatchSentimentResult> {
    const startTime = Date.now();
    const results = request.texts.map(text => this.getFallbackSentiment(text));

    return {
      results,
      processingTime: Date.now() - startTime,
      totalTexts: request.texts.length,
      successCount: results.length,
      errorCount: 0,
    };
  }

  private async updateProductSentimentScores(productId: string): Promise<void> {
    // Calculate aggregated sentiment scores for the product
    const mentions = await prisma.productMention.findMany({
      where: {
        productId,
        sentimentScore: { not: null },
      },
      select: { sentimentScore: true },
    });

    if (mentions.length === 0) return;

    const sentiments = mentions.map(m => Number(m.sentimentScore!));
    const overallScore = sentiments.reduce((sum, s) => sum + s, 0) / sentiments.length;
    const positiveCount = sentiments.filter(s => s > 0.1).length;
    const negativeCount = sentiments.filter(s => s < -0.1).length;
    const neutralCount = sentiments.length - positiveCount - negativeCount;

    await prisma.sentimentScore.upsert({
      where: {
        productId_source: {
          productId,
          source: 'REDDIT'
        }
      },
      update: {
        overallScore,
        confidenceScore: Math.min(0.9, mentions.length / 100), // Higher confidence with more data
        sampleSize: mentions.length,
        positiveScore: positiveCount / mentions.length,
        negativeScore: negativeCount / mentions.length,
        neutralScore: neutralCount / mentions.length,
        lastUpdated: new Date(),
      },
      create: {
        productId,
        source: 'REDDIT',
        overallScore,
        confidenceScore: Math.min(0.9, mentions.length / 100),
        sampleSize: mentions.length,
        positiveScore: positiveCount / mentions.length,
        negativeScore: negativeCount / mentions.length,
        neutralScore: neutralCount / mentions.length,
      },
    });
  }

  private hashText(text: string): string {
    // Simple hash function for caching
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.get('/health');
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }
}
