import { FastifyInstance } from 'fastify';
import { RedditController } from '@/api/controllers/RedditController';
import { asyncHandler } from '@/shared/errors';

const redditController = new RedditController();

export async function redditRoutes(fastify: FastifyInstance) {
  // Search Reddit for product mentions
  fastify.get('/search', {
    schema: {
      tags: ['Reddit'],
      summary: 'Search Reddit for product mentions',
      description: 'Search Reddit posts and comments for product mentions',
      querystring: {
        type: 'object',
        required: ['productName'],
        properties: {
          productName: { type: 'string', minLength: 1, maxLength: 200, description: 'Product name to search for' },
          subreddit: { type: 'string', description: 'Specific subreddit to search' },
          sort: { type: 'string', enum: ['relevance', 'hot', 'top', 'new', 'comments'], default: 'relevance' },
          time: { type: 'string', enum: ['all', 'year', 'month', 'week', 'day', 'hour'], default: 'week' },
          includeComments: { type: 'boolean', default: false, description: 'Include comments in search' },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 25 },
        },
      },
    },
  }, asyncHandler(redditController.searchProductMentions.bind(redditController)));

  // Get trending products from Reddit
  fastify.get('/trending', {
    schema: {
      tags: ['Reddit'],
      summary: 'Get trending products from Reddit',
      description: 'Get trending products based on Reddit mentions',
      querystring: {
        type: 'object',
        properties: {
          timeframe: { type: 'string', enum: ['day', 'week', 'month'], default: 'week' },
          limit: { type: 'integer', minimum: 1, maximum: 50, default: 10 },
          subreddit: { type: 'string', description: 'Filter by specific subreddit' },
        },
      },
    },
  }, asyncHandler(redditController.getTrendingProducts.bind(redditController)));

  // Get popular subreddits
  fastify.get('/subreddits/popular', {
    schema: {
      tags: ['Reddit'],
      summary: 'Get popular product-related subreddits',
      description: 'Get list of popular subreddits for product discussions',
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', minimum: 1, maximum: 50, default: 20 },
        },
      },
    },
  }, asyncHandler(redditController.getPopularSubreddits.bind(redditController)));

  // Get subreddit posts
  fastify.get('/subreddit/:subreddit/posts', {
    schema: {
      tags: ['Reddit'],
      summary: 'Get posts from a subreddit',
      description: 'Get posts from a specific subreddit',
      params: {
        type: 'object',
        required: ['subreddit'],
        properties: {
          subreddit: { type: 'string', description: 'Subreddit name' },
        },
      },
      querystring: {
        type: 'object',
        properties: {
          sort: { type: 'string', enum: ['hot', 'new', 'top'], default: 'hot' },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 25 },
        },
      },
    },
  }, asyncHandler(redditController.getSubredditPosts.bind(redditController)));

  // Get product mentions from Reddit (stored in database)
  fastify.get('/product/:productId/mentions', {
    schema: {
      tags: ['Reddit'],
      summary: 'Get product mentions from Reddit',
      description: 'Get Reddit mentions for a specific product from database',
      params: {
        type: 'object',
        required: ['productId'],
        properties: {
          productId: { type: 'string', format: 'uuid', description: 'Product ID' },
        },
      },
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
        },
      },
    },
  }, asyncHandler(redditController.getProductMentions.bind(redditController)));

  // Save product mentions to database
  fastify.post('/mentions/save', {
    schema: {
      tags: ['Reddit'],
      summary: 'Save product mentions to database',
      description: 'Search and save product mentions from Reddit to database',
      body: {
        type: 'object',
        required: ['productName'],
        properties: {
          productName: { type: 'string', minLength: 1, maxLength: 200 },
        },
      },
    },
  }, asyncHandler(redditController.saveProductMentions.bind(redditController)));

  // Monitor subreddits
  fastify.post('/monitor', {
    schema: {
      tags: ['Reddit'],
      summary: 'Monitor subreddits for keywords',
      description: 'Monitor multiple subreddits for specific keywords',
      body: {
        type: 'object',
        required: ['subreddits', 'keywords'],
        properties: {
          subreddits: { type: 'array', items: { type: 'string' }, minItems: 1, maxItems: 10 },
          keywords: { type: 'array', items: { type: 'string' }, minItems: 1, maxItems: 20 },
          sort: { type: 'string', enum: ['hot', 'new', 'top'], default: 'hot' },
          limit: { type: 'integer', minimum: 1, maximum: 50, default: 25 },
        },
      },
    },
  }, asyncHandler(redditController.monitorSubreddits.bind(redditController)));

  // Get Reddit analytics for a product
  fastify.get('/analytics', {
    schema: {
      tags: ['Reddit'],
      summary: 'Get Reddit analytics for a product',
      description: 'Get comprehensive Reddit analytics for a specific product',
      querystring: {
        type: 'object',
        required: ['productId'],
        properties: {
          productId: { type: 'string', format: 'uuid', description: 'Product ID' },
          timeframe: { type: 'string', enum: ['day', 'week', 'month'], default: 'week' },
        },
      },
    },
  }, asyncHandler(redditController.getRedditAnalytics.bind(redditController)));

  // Reddit health check
  fastify.get('/health', {
    schema: {
      tags: ['Reddit'],
      summary: 'Reddit API health check',
      description: 'Check the health status of Reddit API integration',
    },
  }, asyncHandler(redditController.getRedditHealth.bind(redditController)));
}
