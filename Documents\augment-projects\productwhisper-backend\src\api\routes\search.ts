import { FastifyInstance } from 'fastify';
import { SearchController } from '@/api/controllers/SearchController';
import { async<PERSON>and<PERSON> } from '@/shared/errors';

const searchController = new SearchController();

export async function searchRoutes(fastify: FastifyInstance) {
  // Global product search
  fastify.get('/', {
    schema: {
      tags: ['Search'],
      summary: 'Global product search',
      description: 'Search products across all sources with advanced filtering',
      querystring: {
        type: 'object',
        required: ['q'],
        properties: {
          q: { type: 'string', minLength: 1, maxLength: 200, description: 'Search query' },
          sources: { type: 'array', items: { type: 'string' }, description: 'Filter by sources' },
          sentiment: { type: 'string', enum: ['positive', 'negative', 'neutral'], description: 'Filter by sentiment' },
          dateFrom: { type: 'string', format: 'date-time', description: 'Filter from date' },
          dateTo: { type: 'string', format: 'date-time', description: 'Filter to date' },
          category: { type: 'string', description: 'Filter by category' },
          brand: { type: 'string', description: 'Filter by brand' },
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'array', items: { $ref: '#/definitions/Product' } },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'integer' },
                limit: { type: 'integer' },
                total: { type: 'integer' },
                totalPages: { type: 'integer' },
                hasNext: { type: 'boolean' },
                hasPrev: { type: 'boolean' },
              },
            },
            meta: {
              type: 'object',
              properties: {
                suggestions: { type: 'array', items: { type: 'string' } },
                facets: { type: 'object' },
                query: { type: 'string' },
                filters: { type: 'object' }
              }
            },
            message: { type: 'string' },
            timestamp: { type: 'string' },
            requestId: { type: 'string' },
          },
        },
      },
    },
  }, asyncHandler(searchController.globalSearch.bind(searchController)));

  // Search suggestions/autocomplete
  fastify.get('/suggestions', {
    schema: {
      tags: ['Search'],
      summary: 'Get search suggestions',
      description: 'Get autocomplete suggestions for search queries',
      querystring: {
        type: 'object',
        required: ['q'],
        properties: {
          q: { type: 'string', minLength: 1, maxLength: 100, description: 'Partial search query' },
          limit: { type: 'integer', minimum: 1, maximum: 20, default: 10 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                suggestions: { type: 'array', items: { type: 'string' } },
                query: { type: 'string' }
              }
            },
            message: { type: 'string' },
            timestamp: { type: 'string' },
            requestId: { type: 'string' },
          },
        },
      },
    },
  }, asyncHandler(searchController.getSearchSuggestions.bind(searchController)));

  // Recent searches
  fastify.get('/recent', {
    schema: {
      tags: ['Search'],
      summary: 'Get recent searches',
      description: 'Get recently performed searches',
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', minimum: 1, maximum: 50, default: 10 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                searches: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      query: { type: 'string' },
                      count: { type: 'integer' }
                    }
                  }
                }
              }
            },
            message: { type: 'string' },
            timestamp: { type: 'string' },
            requestId: { type: 'string' },
          },
        },
      },
    },
  }, asyncHandler(searchController.getRecentSearches.bind(searchController)));

  // Popular searches
  fastify.get('/popular', {
    schema: {
      tags: ['Search'],
      summary: 'Get popular searches',
      description: 'Get most popular search queries',
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', minimum: 1, maximum: 50, default: 10 },
          timeframe: { type: 'string', enum: ['day', 'week', 'month'], default: 'week' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                searches: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      query: { type: 'string' },
                      count: { type: 'integer' }
                    }
                  }
                },
                timeframe: { type: 'string' },
                period: { type: 'string' }
              }
            },
            message: { type: 'string' },
            timestamp: { type: 'string' },
            requestId: { type: 'string' },
          },
        },
      },
    },
  }, asyncHandler(searchController.getPopularSearches.bind(searchController)));

  // Search analytics
  fastify.get('/analytics', {
    schema: {
      tags: ['Search'],
      summary: 'Get search analytics',
      description: 'Get comprehensive search analytics and trends',
    },
  }, asyncHandler(searchController.getSearchAnalytics.bind(searchController)));
}
