import { ProductMetadata, ProductExternalIds, PlatformSource } from '@/shared/types';

export interface ProductEntity {
  id: string;
  name: string;
  description?: string;
  category?: string;
  brand?: string;
  imageUrls: string[];
  externalIds: ProductExternalIds;
  metadata: ProductMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductWithSentiment extends ProductEntity {
  sentimentScores: SentimentScoreEntity[];
  mentions: ProductMentionEntity[];
  averageSentiment?: number;
  totalMentions?: number;
  trendScore?: number;
}

export interface SentimentScoreEntity {
  id: string;
  productId: string;
  source: PlatformSource;
  overallScore: number;
  positiveScore?: number;
  negativeScore?: number;
  neutralScore?: number;
  confidenceScore?: number;
  sampleSize: number;
  lastUpdated: Date;
}

export interface ProductMentionEntity {
  id: string;
  productId: string;
  source: PlatformSource;
  sourceId: string;
  content: string;
  sentimentScore?: number;
  engagementMetrics: Record<string, any>;
  url?: string;
  authorInfo: Record<string, any>;
  createdAt?: Date;
  processedAt: Date;
}

export interface SearchAnalyticsEntity {
  id: string;
  query: string;
  resultsCount?: number;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

// Domain methods for Product entity
export class Product {
  constructor(private data: ProductEntity) {}

  get id(): string {
    return this.data.id;
  }

  get name(): string {
    return this.data.name;
  }

  get description(): string | undefined {
    return this.data.description;
  }

  get category(): string | undefined {
    return this.data.category;
  }

  get brand(): string | undefined {
    return this.data.brand;
  }

  get imageUrls(): string[] {
    return this.data.imageUrls;
  }

  get externalIds(): ProductExternalIds {
    return this.data.externalIds;
  }

  get metadata(): ProductMetadata {
    return this.data.metadata;
  }

  get createdAt(): Date {
    return this.data.createdAt;
  }

  get updatedAt(): Date {
    return this.data.updatedAt;
  }

  // Business logic methods
  hasExternalId(platform: string): boolean {
    return !!this.data.externalIds[platform];
  }

  getExternalId(platform: string): string | undefined {
    return this.data.externalIds[platform];
  }

  addExternalId(platform: string, id: string): void {
    this.data.externalIds[platform] = id;
    this.data.updatedAt = new Date();
  }

  updateMetadata(metadata: Partial<ProductMetadata>): void {
    this.data.metadata = { ...this.data.metadata, ...metadata };
    this.data.updatedAt = new Date();
  }

  addImageUrl(url: string): void {
    if (!this.data.imageUrls.includes(url)) {
      this.data.imageUrls.push(url);
      this.data.updatedAt = new Date();
    }
  }

  removeImageUrl(url: string): void {
    const index = this.data.imageUrls.indexOf(url);
    if (index > -1) {
      this.data.imageUrls.splice(index, 1);
      this.data.updatedAt = new Date();
    }
  }

  hasTag(tag: string): boolean {
    return this.data.metadata.tags?.includes(tag) || false;
  }

  addTag(tag: string): void {
    if (!this.data.metadata.tags) {
      this.data.metadata.tags = [];
    }
    if (!this.hasTag(tag)) {
      this.data.metadata.tags.push(tag);
      this.data.updatedAt = new Date();
    }
  }

  removeTag(tag: string): void {
    if (this.data.metadata.tags) {
      const index = this.data.metadata.tags.indexOf(tag);
      if (index > -1) {
        this.data.metadata.tags.splice(index, 1);
        this.data.updatedAt = new Date();
      }
    }
  }

  updatePrice(price: number, currency: string = 'USD'): void {
    this.data.metadata.price = price;
    this.data.metadata.currency = currency;
    this.data.updatedAt = new Date();
  }

  updateRating(rating: number, reviewCount?: number): void {
    this.data.metadata.rating = rating;
    if (reviewCount !== undefined) {
      this.data.metadata.reviewCount = reviewCount;
    }
    this.data.updatedAt = new Date();
  }

  isAvailable(): boolean {
    return this.data.metadata.availability !== 'out_of_stock';
  }

  setAvailability(availability: string): void {
    this.data.metadata.availability = availability;
    this.data.updatedAt = new Date();
  }

  // Validation methods
  isValid(): boolean {
    return !!(
      this.data.name &&
      this.data.name.trim().length > 0 &&
      this.data.name.length <= 255
    );
  }

  getValidationErrors(): string[] {
    const errors: string[] = [];

    if (!this.data.name || this.data.name.trim().length === 0) {
      errors.push('Product name is required');
    }

    if (this.data.name && this.data.name.length > 255) {
      errors.push('Product name must be 255 characters or less');
    }

    if (this.data.description && this.data.description.length > 5000) {
      errors.push('Product description must be 5000 characters or less');
    }

    if (this.data.category && this.data.category.length > 100) {
      errors.push('Product category must be 100 characters or less');
    }

    if (this.data.brand && this.data.brand.length > 100) {
      errors.push('Product brand must be 100 characters or less');
    }

    if (this.data.imageUrls.length > 10) {
      errors.push('Product can have at most 10 image URLs');
    }

    return errors;
  }

  // Serialization
  toJSON(): ProductEntity {
    return { ...this.data };
  }

  toPublicJSON(): Omit<ProductEntity, 'externalIds'> {
    const { externalIds, ...publicData } = this.data;
    return publicData;
  }

  static fromJSON(data: ProductEntity): Product {
    return new Product(data);
  }

  static create(data: Omit<ProductEntity, 'id' | 'createdAt' | 'updatedAt'>): Product {
    const now = new Date();
    return new Product({
      ...data,
      id: '', // Will be set by repository
      createdAt: now,
      updatedAt: now,
    });
  }
}
