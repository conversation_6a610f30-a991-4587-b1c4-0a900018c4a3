import { FastifyRequest, FastifyReply } from 'fastify';
import { SentimentService, SentimentAnalysisRequest, BatchSentimentRequest } from '@/core/services/SentimentService';
import { ValidationError } from '@/shared/errors';
import { z } from 'zod';

const AnalyzeSentimentSchema = z.object({
  text: z.string().min(1).max(5000),
  language: z.string().optional(),
  context: z.enum(['product_review', 'social_media', 'comment', 'general']).default('general'),
});

const BatchAnalyzeSentimentSchema = z.object({
  texts: z.array(z.string().min(1).max(5000)).min(1).max(100),
  language: z.string().optional(),
  context: z.enum(['product_review', 'social_media', 'comment', 'general']).default('general'),
});

const ProductSentimentSchema = z.object({
  productId: z.string().uuid(),
});

const SentimentTrendsSchema = z.object({
  productId: z.string().uuid(),
  timeframe: z.enum(['day', 'week', 'month']).default('week'),
});

export class SentimentController {
  private sentimentService: SentimentService;

  constructor() {
    this.sentimentService = new SentimentService();
  }

  async analyzeSentiment(request: FastifyRequest, reply: FastifyReply) {
    // Validate request body
    const bodyValidation = AnalyzeSentimentSchema.safeParse(request.body);

    if (!bodyValidation.success) {
      throw new ValidationError('Invalid sentiment analysis request', bodyValidation.error.errors);
    }

    const { text, language, context } = bodyValidation.data;

    try {
      const analysisRequest: SentimentAnalysisRequest = {
        text,
        language,
        context,
      };

      const result = await this.sentimentService.analyzeSentiment(analysisRequest);

      return reply.success(
        {
          analysis: result,
          input: {
            text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
            language,
            context,
          },
          timestamp: new Date().toISOString(),
        },
        'Sentiment analysis completed successfully'
      );
    } catch (error) {
      console.error('Sentiment analysis error:', error);
      throw new Error('Failed to analyze sentiment');
    }
  }

  async analyzeBatchSentiment(request: FastifyRequest, reply: FastifyReply) {
    // Validate request body
    const bodyValidation = BatchAnalyzeSentimentSchema.safeParse(request.body);

    if (!bodyValidation.success) {
      throw new ValidationError('Invalid batch sentiment analysis request', bodyValidation.error.errors);
    }

    const { texts, language, context } = bodyValidation.data;

    try {
      const batchRequest: BatchSentimentRequest = {
        texts,
        language,
        context,
      };

      const result = await this.sentimentService.analyzeBatchSentiment(batchRequest);

      return reply.success(
        {
          batchAnalysis: result,
          input: {
            textCount: texts.length,
            language,
            context,
            sampleText: texts[0]?.substring(0, 100) + (texts[0]?.length > 100 ? '...' : ''),
          },
          timestamp: new Date().toISOString(),
        },
        `Batch sentiment analysis completed for ${texts.length} texts`
      );
    } catch (error) {
      console.error('Batch sentiment analysis error:', error);
      throw new Error('Failed to analyze batch sentiment');
    }
  }

  async analyzeProductMentions(request: FastifyRequest, reply: FastifyReply) {
    // Validate request body
    const bodyValidation = ProductSentimentSchema.safeParse(request.body);

    if (!bodyValidation.success) {
      throw new ValidationError('Invalid product sentiment analysis request', bodyValidation.error.errors);
    }

    const { productId } = bodyValidation.data;

    try {
      await this.sentimentService.analyzeProductMentions(productId);

      return reply.success(
        {
          productId,
          status: 'completed',
          timestamp: new Date().toISOString(),
        },
        'Product mentions sentiment analysis completed successfully'
      );
    } catch (error) {
      console.error('Product mentions sentiment analysis error:', error);
      throw new Error('Failed to analyze product mentions sentiment');
    }
  }

  async getSentimentTrends(request: FastifyRequest, reply: FastifyReply) {
    // Validate query parameters
    const queryValidation = SentimentTrendsSchema.safeParse(request.query);

    if (!queryValidation.success) {
      throw new ValidationError('Invalid sentiment trends request', queryValidation.error.errors);
    }

    const { productId, timeframe } = queryValidation.data;

    try {
      const trends = await this.sentimentService.getSentimentTrends(productId, timeframe);

      return reply.success(
        {
          trends,
          productId,
          timeframe,
          generatedAt: new Date().toISOString(),
        },
        'Sentiment trends retrieved successfully'
      );
    } catch (error) {
      console.error('Sentiment trends error:', error);
      throw new Error('Failed to retrieve sentiment trends');
    }
  }

  async getProductSentimentSummary(request: FastifyRequest, reply: FastifyReply) {
    const { productId } = request.params as { productId: string };

    // Validate productId
    if (!productId || !z.string().uuid().safeParse(productId).success) {
      throw new ValidationError('Invalid product ID');
    }

    try {
      const summary = await this.sentimentService.getProductSentimentSummary(productId);

      return reply.success(
        {
          summary,
          productId,
          retrievedAt: new Date().toISOString(),
        },
        'Product sentiment summary retrieved successfully'
      );
    } catch (error) {
      console.error('Product sentiment summary error:', error);
      throw new Error('Failed to retrieve product sentiment summary');
    }
  }

  async getSentimentHealth(request: FastifyRequest, reply: FastifyReply) {
    try {
      const isHealthy = await this.sentimentService.healthCheck();

      return reply.success(
        {
          isHealthy,
          service: 'Sentiment Analysis API',
          timestamp: new Date().toISOString(),
          status: isHealthy ? 'operational' : 'degraded',
        },
        isHealthy ? 'Sentiment service is healthy' : 'Sentiment service has issues'
      );
    } catch (error) {
      console.error('Sentiment health check error:', error);
      return reply.success(
        {
          isHealthy: false,
          service: 'Sentiment Analysis API',
          timestamp: new Date().toISOString(),
          status: 'down',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        'Sentiment service health check failed'
      );
    }
  }

  async getSentimentStats(request: FastifyRequest, reply: FastifyReply) {
    const query = request.query as {
      timeframe?: 'day' | 'week' | 'month';
      productId?: string;
    };

    const timeframe = query.timeframe || 'week';
    const productId = query.productId;

    try {
      // This would typically aggregate sentiment statistics from the database
      // For now, return mock statistics
      const stats = {
        totalAnalyses: 15420,
        averageSentiment: 0.23,
        sentimentDistribution: {
          positive: 6890,
          neutral: 5230,
          negative: 3300,
        },
        topPositiveProducts: [
          { productId: 'prod-1', name: 'iPhone 15 Pro', sentiment: 0.85 },
          { productId: 'prod-2', name: 'Steam Deck', sentiment: 0.78 },
          { productId: 'prod-3', name: 'AirPods Pro 2', sentiment: 0.72 },
        ],
        topNegativeProducts: [
          { productId: 'prod-4', name: 'Product A', sentiment: -0.65 },
          { productId: 'prod-5', name: 'Product B', sentiment: -0.58 },
          { productId: 'prod-6', name: 'Product C', sentiment: -0.45 },
        ],
        timeSeriesData: [
          { date: '2024-01-01', avgSentiment: 0.15, count: 1200 },
          { date: '2024-01-02', avgSentiment: 0.22, count: 1350 },
          { date: '2024-01-03', avgSentiment: 0.28, count: 1180 },
          { date: '2024-01-04', avgSentiment: 0.31, count: 1420 },
          { date: '2024-01-05', avgSentiment: 0.25, count: 1290 },
        ],
        processingMetrics: {
          averageProcessingTime: 245, // milliseconds
          successRate: 98.5,
          errorRate: 1.5,
          cacheHitRate: 76.3,
        },
      };

      return reply.success(
        {
          stats,
          timeframe,
          productId,
          generatedAt: new Date().toISOString(),
        },
        'Sentiment statistics retrieved successfully'
      );
    } catch (error) {
      console.error('Sentiment stats error:', error);
      throw new Error('Failed to retrieve sentiment statistics');
    }
  }

  async compareSentiments(request: FastifyRequest, reply: FastifyReply) {
    const body = request.body as {
      productIds: string[];
      timeframe?: 'day' | 'week' | 'month';
    };

    if (!body.productIds || !Array.isArray(body.productIds) || body.productIds.length < 2) {
      throw new ValidationError('At least 2 product IDs are required for comparison');
    }

    if (body.productIds.length > 10) {
      throw new ValidationError('Maximum 10 products can be compared at once');
    }

    const timeframe = body.timeframe || 'week';

    try {
      const comparisons = await Promise.all(
        body.productIds.map(async (productId) => {
          const [summary, trends] = await Promise.all([
            this.sentimentService.getProductSentimentSummary(productId),
            this.sentimentService.getSentimentTrends(productId, timeframe),
          ]);

          return {
            productId,
            summary,
            trends: trends.summary,
          };
        })
      );

      // Calculate comparison metrics
      const avgSentiments = comparisons.map(c => c.summary.overallScore);
      const bestProduct = comparisons.reduce((best, current) => 
        current.summary.overallScore > best.summary.overallScore ? current : best
      );
      const worstProduct = comparisons.reduce((worst, current) => 
        current.summary.overallScore < worst.summary.overallScore ? current : worst
      );

      return reply.success(
        {
          comparisons,
          analysis: {
            bestPerforming: bestProduct,
            worstPerforming: worstProduct,
            averageSentiment: avgSentiments.reduce((sum, s) => sum + s, 0) / avgSentiments.length,
            sentimentRange: {
              highest: Math.max(...avgSentiments),
              lowest: Math.min(...avgSentiments),
              spread: Math.max(...avgSentiments) - Math.min(...avgSentiments),
            },
          },
          timeframe,
          comparedAt: new Date().toISOString(),
        },
        `Sentiment comparison completed for ${body.productIds.length} products`
      );
    } catch (error) {
      console.error('Sentiment comparison error:', error);
      throw new Error('Failed to compare product sentiments');
    }
  }

  async getSentimentInsights(request: FastifyRequest, reply: FastifyReply) {
    const { productId } = request.params as { productId: string };
    const query = request.query as { timeframe?: 'day' | 'week' | 'month' };

    // Validate productId
    if (!productId || !z.string().uuid().safeParse(productId).success) {
      throw new ValidationError('Invalid product ID');
    }

    const timeframe = query.timeframe || 'week';

    try {
      const [summary, trends] = await Promise.all([
        this.sentimentService.getProductSentimentSummary(productId),
        this.sentimentService.getSentimentTrends(productId, timeframe),
      ]);

      // Generate insights based on the data
      const insights = {
        overallHealth: this.getSentimentHealthLabel(summary.overallScore),
        trendDirection: trends.summary.overallTrend,
        keyFindings: this.generateKeyFindings(summary, trends),
        recommendations: this.generateRecommendations(summary, trends),
        alerts: this.generateAlerts(summary, trends),
        confidence: summary.confidence,
      };

      return reply.success(
        {
          insights,
          summary,
          trends: trends.summary,
          productId,
          timeframe,
          generatedAt: new Date().toISOString(),
        },
        'Sentiment insights generated successfully'
      );
    } catch (error) {
      console.error('Sentiment insights error:', error);
      throw new Error('Failed to generate sentiment insights');
    }
  }

  private getSentimentHealthLabel(score: number): string {
    if (score > 0.5) return 'Excellent';
    if (score > 0.2) return 'Good';
    if (score > -0.2) return 'Neutral';
    if (score > -0.5) return 'Poor';
    return 'Critical';
  }

  private generateKeyFindings(summary: any, trends: any): string[] {
    const findings = [];

    if (summary.overallScore > 0.3) {
      findings.push('Product has strong positive sentiment overall');
    } else if (summary.overallScore < -0.3) {
      findings.push('Product sentiment is concerning and needs attention');
    }

    if (trends.summary.overallTrend === 'improving') {
      findings.push('Sentiment is trending upward over time');
    } else if (trends.summary.overallTrend === 'declining') {
      findings.push('Sentiment is declining and requires investigation');
    }

    if (summary.totalMentions < 10) {
      findings.push('Limited data available - more mentions needed for reliable analysis');
    }

    return findings;
  }

  private generateRecommendations(summary: any, trends: any): string[] {
    const recommendations = [];

    if (summary.overallScore < 0) {
      recommendations.push('Consider investigating negative feedback and addressing common issues');
    }

    if (trends.summary.overallTrend === 'declining') {
      recommendations.push('Monitor recent mentions closely and identify potential causes for sentiment decline');
    }

    if (summary.confidence < 0.5) {
      recommendations.push('Collect more data points to improve sentiment analysis confidence');
    }

    if (summary.distribution.negative > summary.distribution.positive) {
      recommendations.push('Focus on improving product features that are generating negative feedback');
    }

    return recommendations;
  }

  private generateAlerts(summary: any, trends: any): string[] {
    const alerts = [];

    if (summary.overallScore < -0.5) {
      alerts.push('CRITICAL: Product sentiment is severely negative');
    }

    if (trends.summary.overallTrend === 'declining' && summary.overallScore < 0) {
      alerts.push('WARNING: Negative sentiment is worsening over time');
    }

    if (summary.distribution.negative > summary.totalMentions * 0.7) {
      alerts.push('ALERT: Over 70% of mentions are negative');
    }

    return alerts;
  }
}
