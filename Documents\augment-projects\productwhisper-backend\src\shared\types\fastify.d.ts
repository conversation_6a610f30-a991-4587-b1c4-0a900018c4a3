import { FastifyRequest, FastifyReply } from 'fastify';

declare module 'fastify' {
  interface FastifyRequest {
    context: {
      requestId: string;
      startTime: number;
      ip: string;
      userAgent?: string;
      method: string;
      url: string;
    };
  }

  interface FastifyReply {
    success(data: any, message?: string): FastifyReply;
    error(error: string, statusCode?: number): FastifyReply;
    paginated(
      data: any[],
      page: number,
      limit: number,
      total: number,
      message?: string
    ): FastifyReply;
  }
}
