-- ProductWhisper Database Initialization Script
-- This script sets up the database with proper extensions and optimizations

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- <PERSON><PERSON> enum types
DO $$ BEGIN
    CREATE TYPE platform_source AS ENUM (
        'REDDIT',
        'TWITTER', 
        'AMAZON',
        'YOUTUBE',
        'TIKTOK',
        'INSTAGRAM',
        'FACEBOOK',
        'MANUAL'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    brand VARCHAR(100),
    image_urls TEXT[] DEFAULT '{}',
    external_ids JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create sentiment_scores table
CREATE TABLE IF NOT EXISTS sentiment_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    source platform_source NOT NULL,
    overall_score DECIMAL(4,3) CHECK (overall_score >= -1 AND overall_score <= 1),
    positive_score DECIMAL(4,3) CHECK (positive_score >= 0 AND positive_score <= 1),
    negative_score DECIMAL(4,3) CHECK (negative_score >= 0 AND negative_score <= 1),
    neutral_score DECIMAL(4,3) CHECK (neutral_score >= 0 AND neutral_score <= 1),
    confidence_score DECIMAL(4,3) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    sample_size INTEGER DEFAULT 0,
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(product_id, source)
);

-- Create product_mentions table
CREATE TABLE IF NOT EXISTS product_mentions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    source platform_source NOT NULL,
    source_id VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    sentiment_score DECIMAL(4,3) CHECK (sentiment_score >= -1 AND sentiment_score <= 1),
    engagement_metrics JSONB DEFAULT '{}',
    url VARCHAR(500),
    author_info JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ,
    processed_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(source, source_id)
);

-- Create search_analytics table
CREATE TABLE IF NOT EXISTS search_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query VARCHAR(255) NOT NULL,
    results_count INTEGER,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance

-- Products indexes
CREATE INDEX IF NOT EXISTS idx_products_name ON products USING gin(name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_brand ON products(brand);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_products_updated_at ON products(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_products_metadata ON products USING gin(metadata);
CREATE INDEX IF NOT EXISTS idx_products_external_ids ON products USING gin(external_ids);

-- Sentiment scores indexes
CREATE INDEX IF NOT EXISTS idx_sentiment_scores_product_id ON sentiment_scores(product_id);
CREATE INDEX IF NOT EXISTS idx_sentiment_scores_source ON sentiment_scores(source);
CREATE INDEX IF NOT EXISTS idx_sentiment_scores_overall_score ON sentiment_scores(overall_score DESC);
CREATE INDEX IF NOT EXISTS idx_sentiment_scores_last_updated ON sentiment_scores(last_updated DESC);

-- Product mentions indexes
CREATE INDEX IF NOT EXISTS idx_product_mentions_product_id ON product_mentions(product_id);
CREATE INDEX IF NOT EXISTS idx_product_mentions_source ON product_mentions(source);
CREATE INDEX IF NOT EXISTS idx_product_mentions_source_id ON product_mentions(source, source_id);
CREATE INDEX IF NOT EXISTS idx_product_mentions_sentiment_score ON product_mentions(sentiment_score DESC);
CREATE INDEX IF NOT EXISTS idx_product_mentions_created_at ON product_mentions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_product_mentions_processed_at ON product_mentions(processed_at DESC);
CREATE INDEX IF NOT EXISTS idx_product_mentions_content ON product_mentions USING gin(content gin_trgm_ops);

-- Search analytics indexes
CREATE INDEX IF NOT EXISTS idx_search_analytics_query ON search_analytics USING gin(query gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_search_analytics_created_at ON search_analytics(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_search_analytics_ip_address ON search_analytics(ip_address);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for products table
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create views for common queries

-- Product summary view with sentiment
CREATE OR REPLACE VIEW product_summary AS
SELECT 
    p.id,
    p.name,
    p.description,
    p.category,
    p.brand,
    p.image_urls,
    p.metadata,
    p.created_at,
    p.updated_at,
    COALESCE(AVG(ss.overall_score), 0) as avg_sentiment,
    COUNT(pm.id) as mention_count,
    COUNT(DISTINCT ss.source) as sentiment_sources
FROM products p
LEFT JOIN sentiment_scores ss ON p.id = ss.product_id
LEFT JOIN product_mentions pm ON p.id = pm.product_id
GROUP BY p.id, p.name, p.description, p.category, p.brand, p.image_urls, p.metadata, p.created_at, p.updated_at;

-- Trending products view
CREATE OR REPLACE VIEW trending_products AS
SELECT 
    p.id,
    p.name,
    p.category,
    p.brand,
    COUNT(pm.id) as recent_mentions,
    AVG(pm.sentiment_score) as avg_sentiment,
    (COUNT(pm.id) * COALESCE(AVG(pm.sentiment_score), 0)) as trend_score
FROM products p
LEFT JOIN product_mentions pm ON p.id = pm.product_id 
    AND pm.created_at >= NOW() - INTERVAL '7 days'
GROUP BY p.id, p.name, p.category, p.brand
HAVING COUNT(pm.id) > 0
ORDER BY trend_score DESC;

-- Popular search queries view
CREATE OR REPLACE VIEW popular_searches AS
SELECT 
    query,
    COUNT(*) as search_count,
    AVG(results_count) as avg_results,
    MAX(created_at) as last_searched
FROM search_analytics
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY query
ORDER BY search_count DESC;

-- Grant permissions (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO your_app_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO your_app_user;

-- Insert some sample data for testing (optional)
-- This can be removed in production
INSERT INTO products (name, description, category, brand, metadata) VALUES
('iPhone 15 Pro', 'Latest iPhone with advanced features', 'Electronics', 'Apple', '{"price": 999, "currency": "USD"}'),
('MacBook Air M2', 'Lightweight laptop with M2 chip', 'Computers', 'Apple', '{"price": 1199, "currency": "USD"}'),
('Samsung Galaxy S24', 'Android flagship smartphone', 'Electronics', 'Samsung', '{"price": 899, "currency": "USD"}')
ON CONFLICT DO NOTHING;

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'ProductWhisper database initialization completed successfully';
END $$;
