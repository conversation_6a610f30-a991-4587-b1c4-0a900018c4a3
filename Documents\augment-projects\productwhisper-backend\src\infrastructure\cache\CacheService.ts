import { redisCache } from './redis';
import { CacheOptions } from '@/shared/types';
import { cacheConfig } from '@/config';
import { CacheError } from '@/shared/errors';

export class CacheService {
  private redis = redisCache;
  private memoryCache = new Map<string, { value: any; expiry: number }>();
  private maxMemoryCacheSize = 1000; // Maximum number of items in memory cache

  async get<T>(key: string): Promise<T | null> {
    try {
      // Try memory cache first
      const memoryResult = this.getFromMemory<T>(key);
      if (memoryResult !== null) {
        return memoryResult;
      }

      // Try Redis cache
      const redisResult = await this.redis.get<T>(key);
      if (redisResult !== null) {
        // Store in memory cache for faster access
        this.setInMemory(key, redisResult, cacheConfig.ttlShort);
        return redisResult;
      }

      return null;
    } catch (error) {
      console.error(`Cache get error for key ${key}:`, error);
      return null; // Fail silently for cache misses
    }
  }

  async set(key: string, value: any, options: CacheOptions = {}): Promise<boolean> {
    try {
      const ttl = options.ttl || cacheConfig.ttlMedium;

      // Set in memory cache
      this.setInMemory(key, value, ttl);

      // Set in Redis cache
      const redisSuccess = await this.redis.set(key, value, options);

      return redisSuccess;
    } catch (error) {
      console.error(`Cache set error for key ${key}:`, error);
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      // Delete from memory cache
      this.memoryCache.delete(key);

      // Delete from Redis cache
      return await this.redis.del(key);
    } catch (error) {
      console.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      // Check memory cache first
      if (this.memoryCache.has(key)) {
        const item = this.memoryCache.get(key)!;
        if (Date.now() < item.expiry) {
          return true;
        } else {
          this.memoryCache.delete(key);
        }
      }

      // Check Redis cache
      return await this.redis.exists(key);
    } catch (error) {
      console.error(`Cache exists error for key ${key}:`, error);
      return false;
    }
  }

  async ttl(key: string): Promise<number> {
    try {
      // Check memory cache first
      if (this.memoryCache.has(key)) {
        const item = this.memoryCache.get(key)!;
        const remaining = Math.max(0, Math.floor((item.expiry - Date.now()) / 1000));
        if (remaining > 0) {
          return remaining;
        } else {
          this.memoryCache.delete(key);
        }
      }

      // Check Redis cache
      return await this.redis.ttl(key);
    } catch (error) {
      console.error(`Cache TTL error for key ${key}:`, error);
      return -1;
    }
  }

  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    try {
      const results: (T | null)[] = new Array(keys.length).fill(null);
      const redisKeys: string[] = [];
      const redisIndexes: number[] = [];

      // Check memory cache first
      keys.forEach((key, index) => {
        const memoryResult = this.getFromMemory<T>(key);
        if (memoryResult !== null) {
          results[index] = memoryResult;
        } else {
          redisKeys.push(key);
          redisIndexes.push(index);
        }
      });

      // Get remaining keys from Redis
      if (redisKeys.length > 0) {
        const redisResults = await this.redis.mget<T>(redisKeys);
        redisResults.forEach((result, redisIndex) => {
          const originalIndex = redisIndexes[redisIndex];
          results[originalIndex] = result;
          
          // Cache in memory if found
          if (result !== null) {
            this.setInMemory(redisKeys[redisIndex], result, cacheConfig.ttlShort);
          }
        });
      }

      return results;
    } catch (error) {
      console.error('Cache mget error:', error);
      return keys.map(() => null);
    }
  }

  async mset(keyValuePairs: Record<string, any>, ttl?: number): Promise<boolean> {
    try {
      const effectiveTtl = ttl || cacheConfig.ttlMedium;

      // Set in memory cache
      Object.entries(keyValuePairs).forEach(([key, value]) => {
        this.setInMemory(key, value, effectiveTtl);
      });

      // Set in Redis cache
      return await this.redis.mset(keyValuePairs, effectiveTtl);
    } catch (error) {
      console.error('Cache mset error:', error);
      return false;
    }
  }

  async deleteByPattern(pattern: string): Promise<number> {
    try {
      // Clear matching keys from memory cache
      let memoryDeleted = 0;
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      
      for (const key of this.memoryCache.keys()) {
        if (regex.test(key)) {
          this.memoryCache.delete(key);
          memoryDeleted++;
        }
      }

      // Clear matching keys from Redis cache
      const redisDeleted = await this.redis.deleteByPattern(pattern);

      return memoryDeleted + redisDeleted;
    } catch (error) {
      console.error(`Cache delete by pattern error for ${pattern}:`, error);
      return 0;
    }
  }

  async invalidateByTags(tags: string[]): Promise<number> {
    try {
      // Redis handles tag-based invalidation
      const deleted = await this.redis.invalidateByTags(tags);

      // For memory cache, we'd need to track tags separately
      // For now, just clear all memory cache when tags are invalidated
      if (deleted > 0) {
        this.memoryCache.clear();
      }

      return deleted;
    } catch (error) {
      console.error('Cache invalidation by tags error:', error);
      return 0;
    }
  }

  async increment(key: string, by: number = 1): Promise<number> {
    try {
      // Remove from memory cache as we can't guarantee atomicity
      this.memoryCache.delete(key);

      // Use Redis for atomic increment
      return await this.redis.increment(key, by);
    } catch (error) {
      console.error(`Cache increment error for key ${key}:`, error);
      throw new CacheError('Failed to increment cache value');
    }
  }

  async decrement(key: string, by: number = 1): Promise<number> {
    try {
      // Remove from memory cache as we can't guarantee atomicity
      this.memoryCache.delete(key);

      // Use Redis for atomic decrement
      return await this.redis.decrement(key, by);
    } catch (error) {
      console.error(`Cache decrement error for key ${key}:`, error);
      throw new CacheError('Failed to decrement cache value');
    }
  }

  async getStats(): Promise<any> {
    try {
      const redisStats = await this.redis.getStats();
      const memoryStats = {
        size: this.memoryCache.size,
        maxSize: this.maxMemoryCacheSize,
        usage: (this.memoryCache.size / this.maxMemoryCacheSize) * 100,
      };

      return {
        redis: redisStats,
        memory: memoryStats,
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return { redis: { connected: false }, memory: { size: 0 } };
    }
  }

  async flush(): Promise<boolean> {
    try {
      // Clear memory cache
      this.memoryCache.clear();

      // Clear Redis cache
      return await this.redis.flushDb();
    } catch (error) {
      console.error('Cache flush error:', error);
      return false;
    }
  }

  // Memory cache helper methods
  private getFromMemory<T>(key: string): T | null {
    const item = this.memoryCache.get(key);
    if (!item) {
      return null;
    }

    if (Date.now() >= item.expiry) {
      this.memoryCache.delete(key);
      return null;
    }

    return item.value;
  }

  private setInMemory(key: string, value: any, ttlSeconds: number): void {
    // Implement LRU eviction if cache is full
    if (this.memoryCache.size >= this.maxMemoryCacheSize) {
      const firstKey = this.memoryCache.keys().next().value;
      if (firstKey) {
        this.memoryCache.delete(firstKey);
      }
    }

    const expiry = Date.now() + (ttlSeconds * 1000);
    this.memoryCache.set(key, { value, expiry });
  }

  // Cleanup expired memory cache entries
  private cleanupMemoryCache(): void {
    const now = Date.now();
    for (const [key, item] of this.memoryCache.entries()) {
      if (now >= item.expiry) {
        this.memoryCache.delete(key);
      }
    }
  }

  // Start periodic cleanup
  constructor() {
    // Clean up expired memory cache entries every 5 minutes
    setInterval(() => {
      this.cleanupMemoryCache();
    }, 5 * 60 * 1000);
  }
}
