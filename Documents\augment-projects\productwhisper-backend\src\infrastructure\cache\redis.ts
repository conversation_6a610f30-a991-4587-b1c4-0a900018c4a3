import Redis from 'ioredis';
import { databaseConfig, serverConfig, cacheConfig } from '@/config';
import { CacheError } from '@/shared/errors';
import { CacheOptions, CacheKey } from '@/shared/types';
import { createCacheKey } from '@/shared/utils';

class RedisCache {
  private client: Redis;
  private isConnected: boolean = false;

  constructor() {
    this.client = new Redis(databaseConfig.redisUrl, {
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000,
      family: 4,
      keyPrefix: 'productwhisper:',
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.client.on('connect', () => {
      console.log('🔗 Redis connecting...');
    });

    this.client.on('ready', () => {
      this.isConnected = true;
      console.log('✅ Redis connected successfully');
    });

    this.client.on('error', (error) => {
      console.error('❌ Redis error:', error);
      this.isConnected = false;
    });

    this.client.on('close', () => {
      this.isConnected = false;
      console.log('🔌 Redis connection closed');
    });

    this.client.on('reconnecting', () => {
      console.log('🔄 Redis reconnecting...');
    });
  }

  async connect(): Promise<void> {
    try {
      await this.client.connect();
    } catch (error) {
      throw new CacheError('Failed to connect to Redis', { error });
    }
  }

  async disconnect(): Promise<void> {
    await this.client.disconnect();
  }

  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.client.ping();
      return result === 'PONG';
    } catch (error) {
      console.error('Redis health check failed:', error);
      return false;
    }
  }

  // Basic cache operations
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error(`Cache get error for key ${key}:`, error);
      return null; // Fail silently for cache misses
    }
  }

  async set(
    key: string,
    value: any,
    options: CacheOptions = {}
  ): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value);
      const ttl = options.ttl || cacheConfig.ttlMedium;

      if (options.tags && options.tags.length > 0) {
        // Store tags for cache invalidation
        await this.addToTags(key, options.tags);
      }

      await this.client.setex(key, ttl, serialized);
      return true;
    } catch (error) {
      console.error(`Cache set error for key ${key}:`, error);
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      const result = await this.client.del(key);
      return result > 0;
    } catch (error) {
      console.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      console.error(`Cache exists error for key ${key}:`, error);
      return false;
    }
  }

  async ttl(key: string): Promise<number> {
    try {
      return await this.client.ttl(key);
    } catch (error) {
      console.error(`Cache TTL error for key ${key}:`, error);
      return -1;
    }
  }

  // Advanced cache operations
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    try {
      const values = await this.client.mget(...keys);
      return values.map(value => value ? JSON.parse(value) : null);
    } catch (error) {
      console.error('Cache mget error:', error);
      return keys.map(() => null);
    }
  }

  async mset(keyValuePairs: Record<string, any>, ttl?: number): Promise<boolean> {
    try {
      const pipeline = this.client.pipeline();

      Object.entries(keyValuePairs).forEach(([key, value]) => {
        const serialized = JSON.stringify(value);
        if (ttl) {
          pipeline.setex(key, ttl, serialized);
        } else {
          pipeline.set(key, serialized);
        }
      });

      await pipeline.exec();
      return true;
    } catch (error) {
      console.error('Cache mset error:', error);
      return false;
    }
  }

  // Tag-based cache invalidation
  private async addToTags(key: string, tags: string[]): Promise<void> {
    const pipeline = this.client.pipeline();

    tags.forEach(tag => {
      const tagKey = createCacheKey('tags', tag);
      pipeline.sadd(tagKey, key);
      pipeline.expire(tagKey, cacheConfig.ttlLong);
    });

    await pipeline.exec();
  }

  async invalidateByTags(tags: string[]): Promise<number> {
    try {
      let totalInvalidated = 0;

      for (const tag of tags) {
        const tagKey = createCacheKey('tags', tag);
        const keys = await this.client.smembers(tagKey);

        if (keys.length > 0) {
          const deleted = await this.client.del(...keys);
          totalInvalidated += deleted;
        }

        // Remove the tag set itself
        await this.client.del(tagKey);
      }

      return totalInvalidated;
    } catch (error) {
      console.error('Cache invalidation by tags error:', error);
      return 0;
    }
  }

  // Pattern-based operations
  async deleteByPattern(pattern: string): Promise<number> {
    try {
      const keys = await this.client.keys(pattern);
      if (keys.length === 0) return 0;

      return await this.client.del(...keys);
    } catch (error) {
      console.error(`Cache delete by pattern error for ${pattern}:`, error);
      return 0;
    }
  }

  async getKeysByPattern(pattern: string): Promise<string[]> {
    try {
      return await this.client.keys(pattern);
    } catch (error) {
      console.error(`Cache get keys by pattern error for ${pattern}:`, error);
      return [];
    }
  }

  // Atomic operations
  async increment(key: string, by: number = 1): Promise<number> {
    try {
      return await this.client.incrby(key, by);
    } catch (error) {
      console.error(`Cache increment error for key ${key}:`, error);
      throw new CacheError('Failed to increment cache value');
    }
  }

  async decrement(key: string, by: number = 1): Promise<number> {
    try {
      return await this.client.decrby(key, by);
    } catch (error) {
      console.error(`Cache decrement error for key ${key}:`, error);
      throw new CacheError('Failed to decrement cache value');
    }
  }

  // List operations
  async lpush(key: string, ...values: any[]): Promise<number> {
    try {
      const serialized = values.map(v => JSON.stringify(v));
      return await this.client.lpush(key, ...serialized);
    } catch (error) {
      console.error(`Cache lpush error for key ${key}:`, error);
      return 0;
    }
  }

  async rpop<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.rpop(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error(`Cache rpop error for key ${key}:`, error);
      return null;
    }
  }

  // Set operations
  async sadd(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.client.sadd(key, ...members);
    } catch (error) {
      console.error(`Cache sadd error for key ${key}:`, error);
      return 0;
    }
  }

  async smembers(key: string): Promise<string[]> {
    try {
      return await this.client.smembers(key);
    } catch (error) {
      console.error(`Cache smembers error for key ${key}:`, error);
      return [];
    }
  }

  // Cache statistics
  async getStats(): Promise<any> {
    try {
      const info = await this.client.info('memory');
      const keyspace = await this.client.info('keyspace');

      return {
        connected: this.isConnected,
        memory: info,
        keyspace: keyspace,
        uptime: await this.client.info('server'),
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return { connected: false };
    }
  }

  // Flush operations (use with caution)
  async flushAll(): Promise<boolean> {
    try {
      await this.client.flushall();
      return true;
    } catch (error) {
      console.error('Cache flush all error:', error);
      return false;
    }
  }

  async flushDb(): Promise<boolean> {
    try {
      await this.client.flushdb();
      return true;
    } catch (error) {
      console.error('Cache flush db error:', error);
      return false;
    }
  }
}

// Create singleton instance
const redisCache = new RedisCache();

// Initialize connection
redisCache.connect().catch((error) => {
  console.error('Failed to initialize Redis cache:', error);
  // Don't exit process for cache failures in development
  if (serverConfig.nodeEnv === 'production') {
    process.exit(1);
  }
});

// Graceful shutdown
process.on('SIGINT', async () => {
  await redisCache.disconnect();
});

process.on('SIGTERM', async () => {
  await redisCache.disconnect();
});

export { redisCache };
export default redisCache;
