import { z } from 'zod';

// Platform Sources
export enum PlatformSource {
  REDDIT = 'REDDIT',
  TWITTER = 'TWITTER',
  AMAZON = 'AMAZON',
  YOUTUBE = 'YOUTUBE',
  TIKTOK = 'TIKTOK',
  INSTAGRAM = 'INSTAGRAM',
  FACEBOOK = 'FACEBOOK',
  MANUAL = 'MANUAL',
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
  requestId?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Product Types
export interface ProductMetadata {
  price?: number;
  currency?: string;
  availability?: string;
  rating?: number;
  reviewCount?: number;
  features?: string[];
  specifications?: Record<string, any>;
  tags?: string[];
}

export interface ProductExternalIds {
  amazon?: string;
  reddit?: string;
  twitter?: string;
  youtube?: string;
  [key: string]: string | undefined;
}

// Sentiment Analysis Types
export interface SentimentAnalysisResult {
  overallScore: number;
  positiveScore: number;
  negativeScore: number;
  neutralScore: number;
  confidenceScore: number;
  emotions?: {
    joy?: number;
    anger?: number;
    fear?: number;
    sadness?: number;
    surprise?: number;
    disgust?: number;
  };
  keywords?: string[];
  aspects?: {
    aspect: string;
    sentiment: number;
    confidence: number;
  }[];
}

// Engagement Metrics
export interface EngagementMetrics {
  likes?: number;
  dislikes?: number;
  shares?: number;
  comments?: number;
  views?: number;
  upvotes?: number;
  downvotes?: number;
  score?: number;
  replies?: number;
  retweets?: number;
}

// Author Information
export interface AuthorInfo {
  username?: string;
  displayName?: string;
  verified?: boolean;
  followerCount?: number;
  accountAge?: number;
  karma?: number;
  profileUrl?: string;
  avatarUrl?: string;
}

// Search and Filter Types
export interface ProductSearchFilters {
  query?: string;
  category?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  minRating?: number;
  maxRating?: number;
  availability?: string;
  tags?: string[];
  source?: PlatformSource[];
  dateFrom?: Date;
  dateTo?: Date;
}

export interface ProductSearchSort {
  field: 'name' | 'createdAt' | 'updatedAt' | 'sentiment' | 'popularity';
  order: 'asc' | 'desc';
}

export interface PaginationParams {
  page?: number;
  limit?: number;
}

// Reddit API Types
export interface RedditPost {
  id: string;
  title: string;
  selftext: string;
  author: string;
  subreddit: string;
  score: number;
  upvoteRatio: number;
  numComments: number;
  created: number;
  url: string;
  permalink: string;
  thumbnail?: string;
  isVideo: boolean;
  over18: boolean;
}

export interface RedditComment {
  id: string;
  body: string;
  author: string;
  score: number;
  created: number;
  parentId?: string;
  permalink: string;
  replies?: RedditComment[];
}

// Cache Types
export interface CacheOptions {
  ttl?: number;
  tags?: string[];
  namespace?: string;
}

export interface CacheKey {
  key: string;
  namespace?: string;
  tags?: string[];
}

// Rate Limiting Types
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

// Analytics Types
export interface SearchAnalyticsData {
  query: string;
  resultsCount: number;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}

export interface TrendingProduct {
  productId: string;
  name: string;
  category?: string;
  brand?: string;
  mentionCount: number;
  sentimentScore: number;
  trendScore: number;
  imageUrl?: string;
}

// Validation Schemas
export const PaginationSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
});

export const ProductSearchSchema = z.object({
  q: z.string().min(1).max(255).optional(),
  category: z.string().max(100).optional(),
  brand: z.string().max(100).optional(),
  minPrice: z.coerce.number().min(0).optional(),
  maxPrice: z.coerce.number().min(0).optional(),
  minRating: z.coerce.number().min(0).max(5).optional(),
  maxRating: z.coerce.number().min(0).max(5).optional(),
  tags: z.array(z.string()).optional(),
  source: z.array(z.nativeEnum(PlatformSource)).optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt', 'sentiment', 'popularity']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const ProductCreateSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().max(5000).optional(),
  category: z.string().max(100).optional(),
  brand: z.string().max(100).optional(),
  imageUrls: z.array(z.string().url()).default([]),
  externalIds: z.record(z.string()).default({}),
  metadata: z.record(z.any()).default({}),
});

export const SentimentAnalysisSchema = z.object({
  text: z.string().min(1).max(10000),
  productId: z.string().uuid().optional(),
  source: z.nativeEnum(PlatformSource).optional(),
  batchSize: z.coerce.number().min(1).max(100).default(1),
});
