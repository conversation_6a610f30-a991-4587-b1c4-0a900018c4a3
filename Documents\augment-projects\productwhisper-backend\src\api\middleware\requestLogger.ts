import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import fastifyPlugin from 'fastify-plugin';
import { getClientIp } from '@/shared/utils';

interface RequestContext {
  requestId: string;
  startTime: number;
  ip: string;
  userAgent?: string;
  method: string;
  url: string;
}

export const requestLogger = fastifyPlugin(async (fastify: FastifyInstance) => {
  // Hook to log incoming requests
  fastify.addHook('onRequest', async (request: FastifyRequest, reply: FastifyReply) => {
    const startTime = Date.now();
    const ip = getClientIp(request);
    const userAgent = request.headers['user-agent'];

    // Store request context
    (request as any).context = {
      requestId: request.id,
      startTime,
      ip,
      userAgent,
      method: request.method,
      url: request.url,
    };

    // Log incoming request
    request.log.info({
      requestId: request.id,
      method: request.method,
      url: request.url,
      ip,
      userAgent,
      headers: {
        'content-type': request.headers['content-type'],
        'accept': request.headers['accept'],
        'origin': request.headers['origin'],
        'referer': request.headers['referer'],
      },
      query: request.query,
    }, 'Incoming request');
  });

  // Hook to log request completion
  fastify.addHook('onResponse', async (request: FastifyRequest, reply: FastifyReply) => {
    const context = (request as any).context as RequestContext;
    const duration = Date.now() - context.startTime;
    const statusCode = reply.statusCode;

    // Determine log level based on status code
    let logLevel: 'info' | 'warn' | 'error' = 'info';
    if (statusCode >= 400 && statusCode < 500) {
      logLevel = 'warn';
    } else if (statusCode >= 500) {
      logLevel = 'error';
    }

    // Log response
    (request.log as any)[logLevel]({
      requestId: context.requestId,
      method: context.method,
      url: context.url,
      statusCode,
      duration,
      ip: context.ip,
      userAgent: context.userAgent,
      responseSize: reply.getHeader('content-length'),
      responseTime: `${duration}ms`,
    }, `Request completed - ${statusCode} ${context.method} ${context.url}`);

    // Log slow requests
    if (duration > 1000) {
      request.log.warn({
        requestId: context.requestId,
        method: context.method,
        url: context.url,
        duration,
        statusCode,
      }, `Slow request detected - ${duration}ms`);
    }
  });

  // Hook to log errors
  fastify.addHook('onError', async (request: FastifyRequest, reply: FastifyReply, error: Error) => {
    const context = (request as any).context as RequestContext;
    const duration = Date.now() - context.startTime;

    request.log.error({
      requestId: context.requestId,
      method: context.method,
      url: context.url,
      duration,
      ip: context.ip,
      userAgent: context.userAgent,
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name,
      },
    }, 'Request error occurred');
  });

  // Hook to add security headers
  fastify.addHook('onSend', async (request: FastifyRequest, reply: FastifyReply, payload) => {
    const context = (request as any).context as RequestContext;

    // Add custom headers
    reply.header('X-Request-ID', context.requestId);
    reply.header('X-Response-Time', `${Date.now() - context.startTime}ms`);

    // Add security headers if not already set
    if (!reply.getHeader('X-Content-Type-Options')) {
      reply.header('X-Content-Type-Options', 'nosniff');
    }

    if (!reply.getHeader('X-Frame-Options')) {
      reply.header('X-Frame-Options', 'DENY');
    }

    if (!reply.getHeader('X-XSS-Protection')) {
      reply.header('X-XSS-Protection', '1; mode=block');
    }

    return payload;
  });

  // Add request metrics collection
  fastify.addHook('onResponse', async (request: FastifyRequest, reply: FastifyReply) => {
    const context = (request as any).context as RequestContext;
    const duration = Date.now() - context.startTime;
    const statusCode = reply.statusCode;

    // Collect metrics for monitoring
    // This would integrate with Prometheus or other monitoring systems
    const metrics = {
      method: context.method,
      route: request.routerPath || context.url,
      statusCode,
      duration,
      timestamp: new Date().toISOString(),
    };

    // Store metrics in Redis for aggregation (optional)
    try {
      const metricsKey = `metrics:requests:${new Date().toISOString().split('T')[0]}`;
      // This would be implemented with the Redis client
      // await fastify.redis.lpush(metricsKey, JSON.stringify(metrics));
      // await fastify.redis.expire(metricsKey, 86400 * 7); // Keep for 7 days
    } catch (error) {
      // Silently fail metrics collection
      request.log.debug('Failed to store request metrics:', error);
    }
  });

  // Add route-specific logging
  fastify.addHook('preHandler', async (request: FastifyRequest, reply: FastifyReply) => {
    const context = (request as any).context as RequestContext;

    // Log route-specific information
    if (request.routerPath) {
      request.log.debug({
        requestId: context.requestId,
        route: request.routerPath,
        params: request.params,
        query: request.query,
      }, 'Route handler executing');
    }

    // Log request body for POST/PUT/PATCH requests (be careful with sensitive data)
    if (['POST', 'PUT', 'PATCH'].includes(request.method) && request.body) {
      const bodyToLog = sanitizeRequestBody(request.body);
      request.log.debug({
        requestId: context.requestId,
        body: bodyToLog,
      }, 'Request body');
    }
  });
});

// Helper function to sanitize request body for logging
function sanitizeRequestBody(body: any): any {
  if (!body || typeof body !== 'object') {
    return body;
  }

  const sensitiveFields = [
    'password',
    'token',
    'secret',
    'key',
    'authorization',
    'auth',
    'credential',
    'private',
  ];

  const sanitized = { ...body };

  // Recursively sanitize nested objects
  function sanitizeObject(obj: any): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }

    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const lowerKey = key.toLowerCase();
      const isSensitive = sensitiveFields.some(field => lowerKey.includes(field));

      if (isSensitive) {
        result[key] = '[REDACTED]';
      } else if (typeof value === 'object') {
        result[key] = sanitizeObject(value);
      } else {
        result[key] = value;
      }
    }
    return result;
  }

  return sanitizeObject(sanitized);
}
