import { FastifyRequest, FastifyReply } from 'fastify';
import { RedditService } from '@/core/services/RedditService';
import { PaginationSchema } from '@/shared/types';
import { ValidationError } from '@/shared/errors';
import { z } from 'zod';

const SearchMentionsSchema = z.object({
  productName: z.string().min(1).max(200),
  subreddit: z.string().optional(),
  sort: z.enum(['relevance', 'hot', 'top', 'new', 'comments']).default('relevance'),
  time: z.enum(['all', 'year', 'month', 'week', 'day', 'hour']).default('week'),
  includeComments: z.coerce.boolean().default(false),
  limit: z.coerce.number().min(1).max(100).default(25),
});

const MonitorSubredditsSchema = z.object({
  subreddits: z.array(z.string()).min(1).max(10),
  keywords: z.array(z.string()).min(1).max(20),
  sort: z.enum(['hot', 'new', 'top']).default('hot'),
  limit: z.coerce.number().min(1).max(50).default(25),
});

const AnalyticsSchema = z.object({
  productId: z.string().uuid(),
  timeframe: z.enum(['day', 'week', 'month']).default('week'),
});

export class RedditController {
  private redditService: RedditService;

  constructor() {
    this.redditService = new RedditService();
  }

  async searchProductMentions(request: FastifyRequest, reply: FastifyReply) {
    // Validate query parameters
    const queryValidation = SearchMentionsSchema.safeParse(request.query);

    if (!queryValidation.success) {
      throw new ValidationError('Invalid search parameters', queryValidation.error.errors);
    }

    const { productName, subreddit, sort, time, includeComments, limit } = queryValidation.data;

    try {
      const mentions = await this.redditService.searchProductMentions(productName, {
        subreddit,
        sort,
        time,
        limit,
        includeComments,
      });

      return reply.success(
        {
          mentions,
          productName,
          totalFound: mentions.length,
          searchParams: {
            subreddit,
            sort,
            time,
            includeComments,
            limit,
          },
        },
        'Product mentions retrieved successfully from Reddit'
      );
    } catch (error) {
      console.error('Reddit search error:', error);
      throw new Error('Failed to search Reddit for product mentions');
    }
  }

  async getProductMentions(request: FastifyRequest, reply: FastifyReply) {
    const { productId } = request.params as { productId: string };

    // Validate pagination
    const paginationValidation = PaginationSchema.safeParse(request.query);
    if (!paginationValidation.success) {
      throw new ValidationError('Invalid pagination parameters', paginationValidation.error.errors);
    }

    const pagination = paginationValidation.data;

    try {
      const result = await this.redditService.getProductMentionsFromDB(productId, pagination);

      // Set additional metadata in headers
      reply.header('X-Product-Id', productId);
      reply.header('X-Last-Updated', new Date().toISOString());

      return reply.paginated(
        result.mentions,
        pagination.page,
        pagination.limit,
        result.total,
        'Product mentions retrieved successfully'
      );
    } catch (error) {
      console.error('Get product mentions error:', error);
      throw new Error('Failed to retrieve product mentions from database');
    }
  }

  async saveProductMentions(request: FastifyRequest, reply: FastifyReply) {
    const { productName } = request.body as { productName: string };

    if (!productName || typeof productName !== 'string') {
      throw new ValidationError('Product name is required');
    }

    try {
      // Search for mentions
      const mentions = await this.redditService.searchProductMentions(productName, {
        limit: 50,
        includeComments: true,
        time: 'week',
      });

      // Save to database
      await this.redditService.saveProductMentions(mentions);

      return reply.success(
        {
          savedCount: mentions.length,
          productName,
          timestamp: new Date().toISOString(),
        },
        `Successfully saved ${mentions.length} product mentions`
      );
    } catch (error) {
      console.error('Save product mentions error:', error);
      throw new Error('Failed to save product mentions');
    }
  }

  async monitorSubreddits(request: FastifyRequest, reply: FastifyReply) {
    // Validate request body
    const bodyValidation = MonitorSubredditsSchema.safeParse(request.body);

    if (!bodyValidation.success) {
      throw new ValidationError('Invalid monitoring parameters', bodyValidation.error.errors);
    }

    const { subreddits, keywords, sort, limit } = bodyValidation.data;

    try {
      const results = await this.redditService.monitorSubreddits(subreddits, keywords, {
        sort,
        limit,
      });

      const totalPosts = results.reduce((sum, result) => sum + result.posts.length, 0);

      return reply.success(
        {
          results,
          summary: {
            totalSubreddits: subreddits.length,
            totalPosts,
            keywords,
            sort,
            timestamp: new Date().toISOString(),
          },
        },
        `Successfully monitored ${subreddits.length} subreddits and found ${totalPosts} relevant posts`
      );
    } catch (error) {
      console.error('Monitor subreddits error:', error);
      throw new Error('Failed to monitor subreddits');
    }
  }

  async getRedditAnalytics(request: FastifyRequest, reply: FastifyReply) {
    // Validate query parameters
    const queryValidation = AnalyticsSchema.safeParse(request.query);

    if (!queryValidation.success) {
      throw new ValidationError('Invalid analytics parameters', queryValidation.error.errors);
    }

    const { productId, timeframe } = queryValidation.data;

    try {
      const analytics = await this.redditService.getRedditAnalytics(productId, timeframe);

      return reply.success(
        {
          analytics,
          productId,
          timeframe,
          generatedAt: new Date().toISOString(),
        },
        'Reddit analytics retrieved successfully'
      );
    } catch (error) {
      console.error('Reddit analytics error:', error);
      throw new Error('Failed to retrieve Reddit analytics');
    }
  }

  async getSubredditPosts(request: FastifyRequest, reply: FastifyReply) {
    const { subreddit } = request.params as { subreddit: string };
    const query = request.query as {
      sort?: 'hot' | 'new' | 'top';
      limit?: number;
    };

    const sort = query.sort || 'hot';
    const limit = Math.min(query.limit || 25, 100);

    try {
      const posts = await this.redditService.monitorSubreddits([subreddit], [''], {
        sort,
        limit,
      });

      const subredditPosts = posts.find(p => p.subreddit === subreddit)?.posts || [];

      return reply.success(
        {
          posts: subredditPosts,
          subreddit,
          sort,
          limit,
          totalFound: subredditPosts.length,
        },
        `Successfully retrieved posts from r/${subreddit}`
      );
    } catch (error) {
      console.error('Get subreddit posts error:', error);
      throw new Error(`Failed to retrieve posts from r/${subreddit}`);
    }
  }

  async getRedditHealth(request: FastifyRequest, reply: FastifyReply) {
    try {
      const healthStatus = await this.redditService.getRedditHealthStatus();

      return reply.success(
        {
          ...healthStatus,
          timestamp: new Date().toISOString(),
          service: 'Reddit API',
        },
        healthStatus.isHealthy ? 'Reddit API is healthy' : 'Reddit API has issues'
      );
    } catch (error) {
      console.error('Reddit health check error:', error);
      return reply.success(
        {
          isHealthy: false,
          rateLimit: { remaining: 0, resetTime: new Date() },
          lastError: 'Health check failed',
          timestamp: new Date().toISOString(),
          service: 'Reddit API',
        },
        'Reddit API health check failed'
      );
    }
  }

  async getPopularSubreddits(request: FastifyRequest, reply: FastifyReply) {
    const query = request.query as { limit?: number };
    const limit = Math.min(query.limit || 20, 50);

    // This would typically come from a database of tracked subreddits
    // For now, return a static list of popular product-related subreddits
    const popularSubreddits = [
      { name: 'BuyItForLife', description: 'Products that last a lifetime', subscribers: 1200000 },
      { name: 'ProductPorn', description: 'Beautiful product photography', subscribers: 800000 },
      { name: 'shutupandtakemymoney', description: 'Cool products to buy', subscribers: 600000 },
      { name: 'gadgets', description: 'Latest gadgets and technology', subscribers: 18000000 },
      { name: 'technology', description: 'Technology news and discussion', subscribers: 14000000 },
      { name: 'apple', description: 'Apple products and news', subscribers: 3000000 },
      { name: 'android', description: 'Android devices and apps', subscribers: 2500000 },
      { name: 'gaming', description: 'Gaming products and discussion', subscribers: 35000000 },
      { name: 'pcmasterrace', description: 'PC gaming and hardware', subscribers: 5000000 },
      { name: 'buildapc', description: 'PC building advice', subscribers: 4500000 },
      { name: 'headphones', description: 'Headphone reviews and discussion', subscribers: 500000 },
      { name: 'audiophile', description: 'High-quality audio equipment', subscribers: 300000 },
      { name: 'MechanicalKeyboards', description: 'Mechanical keyboard enthusiasts', subscribers: 1000000 },
      { name: 'watches', description: 'Watch enthusiasts', subscribers: 1500000 },
      { name: 'malefashionadvice', description: 'Fashion advice for men', subscribers: 4000000 },
      { name: 'femalefashionadvice', description: 'Fashion advice for women', subscribers: 1200000 },
      { name: 'SkincareAddiction', description: 'Skincare products and advice', subscribers: 1800000 },
      { name: 'MakeupAddiction', description: 'Makeup products and tutorials', subscribers: 1000000 },
      { name: 'fitness', description: 'Fitness equipment and advice', subscribers: 9000000 },
      { name: 'homegym', description: 'Home gym equipment', subscribers: 400000 },
    ].slice(0, limit);

    return reply.success(
      {
        subreddits: popularSubreddits,
        totalCount: popularSubreddits.length,
        limit,
        categories: [
          'Technology',
          'Fashion',
          'Gaming',
          'Audio',
          'Fitness',
          'Beauty',
          'Lifestyle',
        ],
      },
      'Popular product-related subreddits retrieved successfully'
    );
  }

  async getTrendingProducts(request: FastifyRequest, reply: FastifyReply) {
    const query = request.query as {
      timeframe?: 'day' | 'week' | 'month';
      limit?: number;
      subreddit?: string;
    };

    const timeframe = query.timeframe || 'week';
    const limit = Math.min(query.limit || 10, 50);
    const subreddit = query.subreddit;

    try {
      // This would typically analyze recent mentions to find trending products
      // For now, return a mock response
      const trendingProducts = [
        {
          productName: 'iPhone 15 Pro',
          mentionCount: 1250,
          sentimentScore: 0.7,
          topSubreddits: ['apple', 'iphone', 'technology'],
          trendDirection: 'up',
          changePercent: 45.2,
        },
        {
          productName: 'Steam Deck',
          mentionCount: 890,
          sentimentScore: 0.8,
          topSubreddits: ['steamdeck', 'gaming', 'pcgaming'],
          trendDirection: 'up',
          changePercent: 23.1,
        },
        {
          productName: 'AirPods Pro 2',
          mentionCount: 670,
          sentimentScore: 0.6,
          topSubreddits: ['apple', 'headphones', 'audiophile'],
          trendDirection: 'stable',
          changePercent: 2.3,
        },
      ].slice(0, limit);

      return reply.success(
        {
          trendingProducts,
          timeframe,
          limit,
          subreddit,
          generatedAt: new Date().toISOString(),
          summary: {
            totalProducts: trendingProducts.length,
            averageSentiment: trendingProducts.reduce((sum, p) => sum + p.sentimentScore, 0) / trendingProducts.length,
            totalMentions: trendingProducts.reduce((sum, p) => sum + p.mentionCount, 0),
          },
        },
        'Trending products retrieved successfully'
      );
    } catch (error) {
      console.error('Get trending products error:', error);
      throw new Error('Failed to retrieve trending products');
    }
  }
}
